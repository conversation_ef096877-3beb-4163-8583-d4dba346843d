(()=>{var e={};e.id=788,e.ids=[788,660],e.modules={7061:e=>{e.exports={serviceDetailsContainer:"ServiceDetails_serviceDetailsContainer__GaBAr",header:"ServiceDetails_header__tlL8d",breadcrumb:"ServiceDetails_breadcrumb__2VAMz",headerActions:"ServiceDetails_headerActions__Z_zj4",editButton:"ServiceDetails_editButton__Qqmh3",deleteButton:"ServiceDetails_deleteButton__yFIm4",backButton:"ServiceDetails_backButton__2xggn",serviceContent:"ServiceDetails_serviceContent__NDpTn",mainInfo:"ServiceDetails_mainInfo__ipill",serviceHeader:"ServiceDetails_serviceHeader__KS2Z6",serviceName:"ServiceDetails_serviceName__BWoQl",statusBadge:"ServiceDetails_statusBadge__7qYfh",active:"ServiceDetails_active__fyExZ",inactive:"ServiceDetails_inactive__Jondf",draft:"ServiceDetails_draft__1fs8L",description:"ServiceDetails_description__Nl9Uf",detailsGrid:"ServiceDetails_detailsGrid__tPto6",detailCard:"ServiceDetails_detailCard__ldqIO",price:"ServiceDetails_price__uBc3G",duration:"ServiceDetails_duration__wI8Iy",category:"ServiceDetails_category__ZMcNJ",bookings:"ServiceDetails_bookings__6dgcM",metaInfo:"ServiceDetails_metaInfo__7jvIe",metaItem:"ServiceDetails_metaItem__RcID1",sidebar:"ServiceDetails_sidebar__HP2sl",quickActions:"ServiceDetails_quickActions___TqtS",relatedInfo:"ServiceDetails_relatedInfo__t8OFd",actionButton:"ServiceDetails_actionButton__uDU0A",visibilityOptions:"ServiceDetails_visibilityOptions__0Q92P",visibilityItem:"ServiceDetails_visibilityItem__44fmZ",enabled:"ServiceDetails_enabled__EzSAL",disabled:"ServiceDetails_disabled__jJylp",loadingContainer:"ServiceDetails_loadingContainer__Wjus8",errorContainer:"ServiceDetails_errorContainer__8Beaj",notFoundContainer:"ServiceDetails_notFoundContainer__qJlmD",loadingSpinner:"ServiceDetails_loadingSpinner__dvmHO",spin:"ServiceDetails_spin__8qCNm"}},1395:(e,i,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(i),s.d(i,{config:()=>x,default:()=>_,getServerSideProps:()=>u,getStaticPaths:()=>m,getStaticProps:()=>h,reportWebVitals:()=>S,routeModule:()=>N,unstable_getServerProps:()=>D,unstable_getServerSideProps:()=>g,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>p});var a=s(7093),r=s(5244),n=s(1323),c=s(2899),l=s.n(c),d=s(6814),o=s(6835),v=e([d,o]);[d,o]=v.then?(await v)():v;let _=(0,n.l)(o,"default"),h=(0,n.l)(o,"getStaticProps"),m=(0,n.l)(o,"getStaticPaths"),u=(0,n.l)(o,"getServerSideProps"),x=(0,n.l)(o,"config"),S=(0,n.l)(o,"reportWebVitals"),p=(0,n.l)(o,"unstable_getStaticProps"),j=(0,n.l)(o,"unstable_getStaticPaths"),b=(0,n.l)(o,"unstable_getStaticParams"),D=(0,n.l)(o,"unstable_getServerProps"),g=(0,n.l)(o,"unstable_getServerSideProps"),N=new a.PagesRouteModule({definition:{kind:r.x.PAGES,page:"/admin/services/[id]",pathname:"/admin/services/[id]",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:o});t()}catch(e){t(e)}})},6835:(e,i,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(i),s.d(i,{default:()=>x});var a=s(997),r=s(6689),n=s(1163),c=s(968),l=s.n(c),d=s(1664),o=s.n(d),v=s(8568),_=s(4845),h=s(7061),m=s.n(h),u=e([_]);function x(){let e;let i=(0,n.useRouter)(),{id:s}=i.query,{user:t,loading:c}=(0,v.a)(),[d,h]=(0,r.useState)(!0),[u,x]=(0,r.useState)(null),[S,p]=(0,r.useState)(null),j=async()=>{if(confirm("Are you sure you want to delete this service? This action cannot be undone."))try{let e=localStorage.getItem("admin-token");if(!(await fetch(`/api/admin/services/${s}`,{method:"DELETE",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}})).ok)throw Error("Failed to delete service");i.push("/admin/services")}catch(e){console.error("Error deleting service:",e),alert("Failed to delete service: "+e.message)}};return c||d?a.jsx(_.Z,{children:(0,a.jsxs)("div",{className:m().loadingContainer,children:[a.jsx("div",{className:m().loadingSpinner}),a.jsx("p",{children:"Loading service details..."})]})}):S?a.jsx(_.Z,{children:(0,a.jsxs)("div",{className:m().errorContainer,children:[a.jsx("h2",{children:"Error Loading Service"}),a.jsx("p",{children:S}),a.jsx(o(),{href:"/admin/services",className:m().backButton,children:"← Back to Services"})]})}):u?(0,a.jsxs)(_.Z,{children:[(0,a.jsxs)(l(),{children:[(0,a.jsxs)("title",{children:[u.name," - Service Details | Ocean Soul Sparkles Admin"]}),a.jsx("meta",{name:"description",content:`Details for ${u.name} service`})]}),(0,a.jsxs)("div",{className:m().serviceDetailsContainer,children:[(0,a.jsxs)("header",{className:m().header,children:[(0,a.jsxs)("div",{className:m().breadcrumb,children:[a.jsx(o(),{href:"/admin/services",children:"Services"}),a.jsx("span",{children:"/"}),a.jsx("span",{children:u.name})]}),(0,a.jsxs)("div",{className:m().headerActions,children:[a.jsx(o(),{href:`/admin/services/${u.id}/edit`,className:m().editButton,children:"✏️ Edit Service"}),a.jsx("button",{onClick:j,className:m().deleteButton,children:"\uD83D\uDDD1️ Delete"}),a.jsx(o(),{href:"/admin/services",className:m().backButton,children:"← Back to Services"})]})]}),(0,a.jsxs)("div",{className:m().serviceContent,children:[(0,a.jsxs)("div",{className:m().mainInfo,children:[(0,a.jsxs)("div",{className:m().serviceHeader,children:[a.jsx("h1",{className:m().serviceName,children:u.name}),a.jsx("span",{className:`${m().statusBadge} ${m()[u.status]}`,children:u.status})]}),u.description&&(0,a.jsxs)("div",{className:m().description,children:[a.jsx("h3",{children:"Description"}),a.jsx("p",{children:u.description})]}),(0,a.jsxs)("div",{className:m().detailsGrid,children:[(0,a.jsxs)("div",{className:m().detailCard,children:[a.jsx("h4",{children:"Pricing"}),a.jsx("div",{className:m().price,children:u.price?(e=u.price,new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e)):"Contact for pricing"})]}),(0,a.jsxs)("div",{className:m().detailCard,children:[a.jsx("h4",{children:"Duration"}),a.jsx("div",{className:m().duration,children:u.duration?(e=>{if(e>=60){let i=Math.floor(e/60),s=e%60;return s>0?`${i}h ${s}m`:`${i}h`}return`${e}m`})(u.duration):"Variable"})]}),(0,a.jsxs)("div",{className:m().detailCard,children:[a.jsx("h4",{children:"Category"}),a.jsx("div",{className:m().category,children:u.category})]}),(0,a.jsxs)("div",{className:m().detailCard,children:[a.jsx("h4",{children:"Total Bookings"}),a.jsx("div",{className:m().bookings,children:u.total_bookings||0})]})]}),(0,a.jsxs)("div",{className:m().metaInfo,children:[(0,a.jsxs)("div",{className:m().metaItem,children:[a.jsx("strong",{children:"Created:"})," ",new Date(u.created_at).toLocaleDateString()]}),(0,a.jsxs)("div",{className:m().metaItem,children:[a.jsx("strong",{children:"Last Updated:"})," ",new Date(u.updated_at).toLocaleDateString()]}),(0,a.jsxs)("div",{className:m().metaItem,children:[a.jsx("strong",{children:"Service ID:"})," ",u.id]})]})]}),(0,a.jsxs)("div",{className:m().sidebar,children:[(0,a.jsxs)("div",{className:m().quickActions,children:[a.jsx("h3",{children:"Quick Actions"}),a.jsx(o(),{href:`/admin/services/${u.id}/edit`,className:m().actionButton,children:"Edit Service Details"}),a.jsx(o(),{href:`/admin/bookings/new?service=${u.id}`,className:m().actionButton,children:"Create Booking"}),a.jsx(o(),{href:`/admin/services/${u.id}/analytics`,className:m().actionButton,children:"View Analytics"})]}),(0,a.jsxs)("div",{className:m().relatedInfo,children:[a.jsx("h3",{children:"Service Visibility"}),(0,a.jsxs)("div",{className:m().visibilityOptions,children:[(0,a.jsxs)("div",{className:m().visibilityItem,children:[a.jsx("span",{children:"Public Website:"}),a.jsx("span",{className:u.visible_on_public?m().enabled:m().disabled,children:u.visible_on_public?"Visible":"Hidden"})]}),(0,a.jsxs)("div",{className:m().visibilityItem,children:[a.jsx("span",{children:"POS System:"}),a.jsx("span",{className:u.visible_on_pos?m().enabled:m().disabled,children:u.visible_on_pos?"Available":"Hidden"})]}),(0,a.jsxs)("div",{className:m().visibilityItem,children:[a.jsx("span",{children:"Events:"}),a.jsx("span",{className:u.visible_on_events?m().enabled:m().disabled,children:u.visible_on_events?"Available":"Hidden"})]})]})]})]})]})]})]}):a.jsx(_.Z,{children:(0,a.jsxs)("div",{className:m().notFoundContainer,children:[a.jsx("h2",{children:"Service Not Found"}),a.jsx("p",{children:"The service you're looking for doesn't exist."}),a.jsx(o(),{href:"/admin/services",className:m().backButton,children:"← Back to Services"})]})})}_=(u.then?(await u)():u)[0],t()}catch(e){t(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var i=require("../../../webpack-runtime.js");i.C(e);var s=e=>i(i.s=e),t=i.X(0,[899,212,664,441],()=>s(1395));module.exports=t})();