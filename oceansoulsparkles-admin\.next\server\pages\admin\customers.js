(()=>{var e={};e.id=939,e.ids=[939,660],e.modules={5273:e=>{e.exports={customersContainer:"Customers_customersContainer___hRzb",header:"Customers_header__Xsb8L",title:"Customers_title__kuVdg",headerActions:"Customers_headerActions__wW_iz",newCustomerBtn:"Customers_newCustomerBtn__ACkta",backButton:"Customers_backButton__mwCeO",controlsPanel:"Customers_controlsPanel__QNdpL",searchSection:"Customers_searchSection__sgj9I",searchInput:"Customers_searchInput__BB_0d",sortSection:"Customers_sortSection__qb0FO",sortSelect:"Customers_sortSelect__oahlL",sortOrderBtn:"Customers_sortOrderBtn__t2r88",customersContent:"Customers_customersContent__z_zag",customersHeader:"Customers_customersHeader__einsi",statsCards:"Customers_statsCards__ph2BM",statCard:"Customers_statCard__Pua_g",statValue:"Customers_statValue__357cS",emptyState:"Customers_emptyState__umGfV",customersGrid:"Customers_customersGrid__nkG51",customerCard:"Customers_customerCard__oECGs",customerHeader:"Customers_customerHeader__QfMYw",customerInfo:"Customers_customerInfo__sYPpi",statusBadge:"Customers_statusBadge__nVYbG",statusVip:"Customers_statusVip__GAam3",statusActive:"Customers_statusActive__N_jFf",statusNew:"Customers_statusNew__8r02c",statusInactive:"Customers_statusInactive__inD5l",statusDefault:"Customers_statusDefault__R5R8L",customerStats:"Customers_customerStats__OLqld",statItem:"Customers_statItem__YazVS",statLabel:"Customers_statLabel__pm6R5",customerNotes:"Customers_customerNotes__KM6Mg",customerActions:"Customers_customerActions__TS_QE",viewBtn:"Customers_viewBtn__TNPmH",bookBtn:"Customers_bookBtn__333XR",loadingContainer:"Customers_loadingContainer__eyNOh",loadingSpinner:"Customers_loadingSpinner__2d9G_",spin:"Customers_spin__xe_Ia"}},5236:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{config:()=>p,default:()=>l,getServerSideProps:()=>C,getStaticPaths:()=>h,getStaticProps:()=>d,reportWebVitals:()=>x,routeModule:()=>b,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>S});var a=t(7093),o=t(5244),n=t(1323),i=t(2899),u=t.n(i),c=t(6814),m=t(1005),_=e([c,m]);[c,m]=_.then?(await _)():_;let l=(0,n.l)(m,"default"),d=(0,n.l)(m,"getStaticProps"),h=(0,n.l)(m,"getStaticPaths"),C=(0,n.l)(m,"getServerSideProps"),p=(0,n.l)(m,"config"),x=(0,n.l)(m,"reportWebVitals"),S=(0,n.l)(m,"unstable_getStaticProps"),g=(0,n.l)(m,"unstable_getStaticPaths"),j=(0,n.l)(m,"unstable_getStaticParams"),v=(0,n.l)(m,"unstable_getServerProps"),N=(0,n.l)(m,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/admin/customers",pathname:"/admin/customers",bundlePath:"",filename:""},components:{App:c.default,Document:u()},userland:m});r()}catch(e){r(e)}})},1005:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{default:()=>d});var a=t(997),o=t(6689),n=t(968),i=t.n(n),u=t(4845),c=t(8568),m=t(5273),_=t.n(m),l=e([u]);function d(){let{user:e,loading:s}=(0,c.a)(),[t,r]=(0,o.useState)([]),[n,m]=(0,o.useState)([]),[l,d]=(0,o.useState)(!0),[h,C]=(0,o.useState)("");return s||l?a.jsx(u.Z,{children:(0,a.jsxs)("div",{className:_().loadingContainer,children:[a.jsx("div",{className:_().loadingSpinner}),a.jsx("p",{children:"Loading customers..."})]})}):(0,a.jsxs)(u.Z,{children:[a.jsx(i(),{children:a.jsx("title",{children:"Customers Management - Ocean Soul Sparkles Admin"})}),(0,a.jsxs)("div",{className:_().customersContainer,children:[(0,a.jsxs)("header",{className:_().header,children:[a.jsx("h1",{children:"Customers Management"}),a.jsx("div",{className:_().headerActions,children:a.jsx("button",{className:_().newCustomerBtn,children:"+ New Customer"})})]}),a.jsx("div",{className:_().controlsPanel,children:a.jsx("div",{className:_().searchContainer,children:a.jsx("input",{type:"text",placeholder:"Search customers...",value:h,onChange:e=>C(e.target.value),className:_().searchInput})})}),a.jsx("div",{className:_().customersGrid,children:0===n.length?a.jsx("div",{className:_().emptyState,children:a.jsx("p",{children:"No customers found."})}):n.map(e=>(0,a.jsxs)("div",{className:_().customerCard,children:[(0,a.jsxs)("div",{className:_().customerHeader,children:[a.jsx("h3",{children:e.name}),(0,a.jsxs)("span",{className:_().bookingCount,children:[e.total_bookings," bookings"]})]}),(0,a.jsxs)("div",{className:_().customerDetails,children:[a.jsx("p",{className:_().customerEmail,children:e.email}),a.jsx("p",{className:_().customerPhone,children:e.phone}),e.notes&&a.jsx("p",{className:_().customerNotes,children:e.notes})]}),(0,a.jsxs)("div",{className:_().customerActions,children:[a.jsx(Link,{href:`/admin/customers/${e.id}`,className:_().viewBtn,children:"View Details"}),a.jsx(Link,{href:`/admin/customers/${e.id}/edit`,className:_().editBtn,children:"Edit"})]})]},e.id))})]})]})}u=(l.then?(await l)():l)[0],r()}catch(e){r(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[899,212,664,441],()=>t(5236));module.exports=r})();