import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    // Get services from database
    const { data: services, error } = await supabase
      .from('services')
      .select(`
        id,
        name,
        description,
        duration,
        price,
        category,
        status,
        created_at,
        updated_at
      `)
      .order('category', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Services query error:', error);
      return res.status(500).json({ error: 'Failed to fetch services' });
    }

    // Get booking counts for each service
    const serviceIds = services?.map(s => s.id) || [];
    const { data: bookingCounts } = await supabase
      .from('bookings')
      .select('service_id')
      .in('service_id', serviceIds);

    // Count bookings per service
    const bookingCountMap = (bookingCounts || []).reduce((acc, booking) => {
      acc[booking.service_id] = (acc[booking.service_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Transform data
    const transformedServices = (services || []).map(service => ({
      id: service.id,
      name: service.name,
      description: service.description,
      duration: service.duration,
      price: service.price,
      category: service.category,
      is_active: service.status === 'active',
      status: service.status,
      total_bookings: bookingCountMap[service.id] || 0,
      created_at: service.created_at,
      updated_at: service.updated_at
    }));

    return res.status(200).json({
      services: transformedServices,
      total: transformedServices.length
    });

  } catch (error) {
    console.error('Services API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
