(()=>{var e={};e.id=371,e.ids=[371,660],e.modules={9722:e=>{e.exports={inventoryContainer:"Inventory_inventoryContainer__GcfkW",header:"Inventory_header__YsGK_",title:"Inventory_title__Y0smo",headerActions:"Inventory_headerActions__sakeA",newItemBtn:"Inventory_newItemBtn__L0yQN",backButton:"Inventory_backButton__j1uiL",controlsPanel:"Inventory_controlsPanel__ptM_3",searchSection:"Inventory_searchSection__Y9Hgr",searchInput:"Inventory_searchInput__MExRL",filters:"Inventory_filters__LHQyS",categoryFilter:"Inventory_categoryFilter__0FZb_",stockFilter:"Inventory_stockFilter__Fq41i",sortSelect:"Inventory_sortSelect__w6Bd6",inventoryContent:"Inventory_inventoryContent__MZtM5",statsCards:"Inventory_statsCards__zQ0vN",statCard:"Inventory_statCard__281a_",statValue:"Inventory_statValue__fmJux",emptyState:"Inventory_emptyState__JwMRE",inventoryGrid:"Inventory_inventoryGrid__6nI_9",inventoryCard:"Inventory_inventoryCard__h1i83",itemHeader:"Inventory_itemHeader__6a78v",itemInfo:"Inventory_itemInfo__6vyyD",category:"Inventory_category__uUYPL",statusBadge:"Inventory_statusBadge__293g1",statusInStock:"Inventory_statusInStock__4rFKW",statusLowStock:"Inventory_statusLowStock__mOjky",statusCritical:"Inventory_statusCritical__iI7Nz",statusOutOfStock:"Inventory_statusOutOfStock__8GWFB",statusDefault:"Inventory_statusDefault__bCVl8",itemDetails:"Inventory_itemDetails__Jlyif",stockInfo:"Inventory_stockInfo__dQ4ll",stockLevel:"Inventory_stockLevel__0YpyD",currentStock:"Inventory_currentStock__0ZyIE",stockRange:"Inventory_stockRange__tajLE",stockBar:"Inventory_stockBar__R6ZDA",stockFill:"Inventory_stockFill__IbWUp",itemStats:"Inventory_itemStats__orFkd",statItem:"Inventory_statItem__Q085T",statLabel:"Inventory_statLabel__yHJ_o",itemActions:"Inventory_itemActions__F5Ub9",editBtn:"Inventory_editBtn__Gdnet",restockBtn:"Inventory_restockBtn__yaupq",loadingContainer:"Inventory_loadingContainer__28CuE",loadingSpinner:"Inventory_loadingSpinner__DNa6M",spin:"Inventory_spin__kif5C"}},6614:(e,t,s)=>{"use strict";s.a(e,async(e,n)=>{try{s.r(t),s.d(t,{config:()=>m,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>y,getStaticProps:()=>v,reportWebVitals:()=>p,routeModule:()=>k,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>g,unstable_getStaticParams:()=>I,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>x});var r=s(7093),a=s(5244),i=s(1323),o=s(2899),l=s.n(o),c=s(6814),d=s(1586),_=e([c,d]);[c,d]=_.then?(await _)():_;let u=(0,i.l)(d,"default"),v=(0,i.l)(d,"getStaticProps"),y=(0,i.l)(d,"getStaticPaths"),h=(0,i.l)(d,"getServerSideProps"),m=(0,i.l)(d,"config"),p=(0,i.l)(d,"reportWebVitals"),x=(0,i.l)(d,"unstable_getStaticProps"),j=(0,i.l)(d,"unstable_getStaticPaths"),I=(0,i.l)(d,"unstable_getStaticParams"),S=(0,i.l)(d,"unstable_getServerProps"),g=(0,i.l)(d,"unstable_getServerSideProps"),k=new r.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/admin/inventory",pathname:"/admin/inventory",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});n()}catch(e){n(e)}})},1586:(e,t,s)=>{"use strict";s.a(e,async(e,n)=>{try{s.r(t),s.d(t,{default:()=>h});var r=s(997),a=s(6689),i=s(968),o=s.n(i),l=s(1664),c=s.n(l),d=s(8568),_=s(4845),u=s(9722),v=s.n(u),y=e([_]);function h(){let{user:e,loading:t}=(0,d.a)(),[s,n]=(0,a.useState)(!0),[i,l]=(0,a.useState)([]),[u,y]=(0,a.useState)([]),[h,m]=(0,a.useState)(""),[p,x]=(0,a.useState)("all"),[j,I]=(0,a.useState)("all"),[S,g]=(0,a.useState)("name"),k=e=>{switch(e){case"in_stock":return"#22c55e";case"low_stock":return"#f59e0b";case"out_of_stock":return"#ef4444";default:return"#6b7280"}},N=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e);return t||s?r.jsx(_.Z,{children:(0,r.jsxs)("div",{className:v().loadingContainer,children:[r.jsx("div",{className:v().loadingSpinner}),r.jsx("p",{children:"Loading inventory..."})]})}):e?(0,r.jsxs)(_.Z,{children:[(0,r.jsxs)(o(),{children:[r.jsx("title",{children:"Inventory Management | Ocean Soul Sparkles Admin"}),r.jsx("meta",{name:"description",content:"Manage product inventory and stock levels"})]}),(0,r.jsxs)("div",{className:v().inventoryContainer,children:[(0,r.jsxs)("header",{className:v().header,children:[r.jsx("h1",{className:v().title,children:"Inventory Management"}),r.jsx("div",{className:v().headerActions,children:r.jsx(c(),{href:"/admin/inventory/new",className:v().newItemBtn,children:"+ Add Product"})})]}),(0,r.jsxs)("div",{className:v().controlsPanel,children:[r.jsx("div",{className:v().searchSection,children:r.jsx("input",{type:"text",placeholder:"Search products by name, category, or SKU...",value:h,onChange:e=>m(e.target.value),className:v().searchInput})}),(0,r.jsxs)("div",{className:v().filtersSection,children:[(0,r.jsxs)("div",{className:v().filterGroup,children:[r.jsx("label",{children:"Category:"}),(0,r.jsxs)("select",{value:p,onChange:e=>x(e.target.value),className:v().filterSelect,children:[r.jsx("option",{value:"all",children:"All Categories"}),r.jsx("option",{value:"Braiding Hair",children:"Braiding Hair"}),r.jsx("option",{value:"Marley Hair",children:"Marley Hair"}),r.jsx("option",{value:"Hair Products",children:"Hair Products"}),r.jsx("option",{value:"Accessories",children:"Accessories"})]})]}),(0,r.jsxs)("div",{className:v().filterGroup,children:[r.jsx("label",{children:"Stock Status:"}),(0,r.jsxs)("select",{value:j,onChange:e=>I(e.target.value),className:v().filterSelect,children:[r.jsx("option",{value:"all",children:"All Stock Levels"}),r.jsx("option",{value:"in_stock",children:"In Stock"}),r.jsx("option",{value:"low_stock",children:"Low Stock"}),r.jsx("option",{value:"out_of_stock",children:"Out of Stock"})]})]}),(0,r.jsxs)("div",{className:v().filterGroup,children:[r.jsx("label",{children:"Sort by:"}),(0,r.jsxs)("select",{value:S,onChange:e=>g(e.target.value),className:v().filterSelect,children:[r.jsx("option",{value:"name",children:"Name"}),r.jsx("option",{value:"category",children:"Category"}),r.jsx("option",{value:"stock",children:"Stock Level"}),r.jsx("option",{value:"price",children:"Price"})]})]})]})]}),r.jsx("div",{className:v().inventoryContent,children:0===u.length?(0,r.jsxs)("div",{className:v().emptyState,children:[r.jsx("h3",{children:"No inventory items found"}),r.jsx("p",{children:0===i.length?"Get started by adding your first product to the inventory.":"Try adjusting your search or filter criteria."}),r.jsx(c(),{href:"/admin/inventory/new",className:v().addFirstBtn,children:"Add First Product"})]}):r.jsx("div",{className:v().inventoryGrid,children:u.map(e=>(0,r.jsxs)("div",{className:v().inventoryCard,children:[(0,r.jsxs)("div",{className:v().cardHeader,children:[r.jsx("h3",{className:v().itemName,children:e.name}),r.jsx("span",{className:v().statusBadge,style:{backgroundColor:k(e.status)},children:e.status.replace("_"," ").toUpperCase()})]}),(0,r.jsxs)("div",{className:v().cardBody,children:[(0,r.jsxs)("div",{className:v().itemInfo,children:[r.jsx("p",{className:v().category,children:e.category}),e.sku&&(0,r.jsxs)("p",{className:v().sku,children:["SKU: ",e.sku]}),e.description&&r.jsx("p",{className:v().description,children:e.description})]}),(0,r.jsxs)("div",{className:v().stockInfo,children:[(0,r.jsxs)("div",{className:v().stockLevel,children:[r.jsx("span",{className:v().label,children:"Stock:"}),(0,r.jsxs)("span",{className:v().value,children:[e.stock_quantity||0,e.min_stock_level&&` (Min: ${e.min_stock_level})`]})]}),e.sale_price&&(0,r.jsxs)("div",{className:v().priceInfo,children:[r.jsx("span",{className:v().label,children:"Price:"}),r.jsx("span",{className:v().value,children:N(e.sale_price)})]}),e.supplier&&(0,r.jsxs)("div",{className:v().supplierInfo,children:[r.jsx("span",{className:v().label,children:"Supplier:"}),r.jsx("span",{className:v().value,children:e.supplier})]})]})]}),(0,r.jsxs)("div",{className:v().cardActions,children:[r.jsx(c(),{href:`/admin/inventory/${e.id}`,className:v().viewBtn,children:"View Details"}),r.jsx("button",{className:v().editBtn,children:"Edit"}),(e.stock_quantity||0)<=(e.min_stock_level||0)&&r.jsx("button",{className:v().restockBtn,children:"Restock"})]})]},e.id))})})]})]}):null}_=(y.then?(await y)():y)[0],n()}catch(e){n(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[899,212,664,441],()=>s(6614));module.exports=n})();