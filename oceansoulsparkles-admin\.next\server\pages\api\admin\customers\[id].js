"use strict";(()=>{var e={};e.id=277,e.ids=[277],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},1089:(e,r,t)=>{t.r(r),t.d(r,{config:()=>p,default:()=>m,routeModule:()=>f});var s={};t.r(s),t.d(s,{default:()=>c});var o=t(1802),a=t(7153),n=t(8781),u=t(7474),i=t(2885);let d=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,i.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",d);async function c(e,r){let{id:t}=e.query;if(!t||"string"!=typeof t)return r.status(400).json({error:"Customer ID is required"});try{let s=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!s)return r.status(401).json({error:"No authentication token"});let o=await (0,u.Wg)(s);if(!o.valid||!o.user)return r.status(401).json({error:"Invalid authentication"});let a=o.user;if("GET"===e.method){let{data:e,error:s}=await l.from("customers").select(`
          id,
          first_name,
          last_name,
          email,
          phone,
          phone_secondary,
          date_of_birth,
          address,
          notes,
          created_at,
          updated_at
        `).eq("id",t).single();if(s)return console.error("Customer query error:",s),r.status(500).json({error:"Failed to fetch customer"});if(!e)return r.status(404).json({error:"Customer not found"});let{count:o}=await l.from("bookings").select("*",{count:"exact",head:!0}).eq("customer_id",t),a={...e,total_bookings:o||0};return r.status(200).json({customer:a})}if("PUT"===e.method){let{first_name:s,last_name:o,email:a,phone:n,phone_secondary:u,date_of_birth:i,address:d,notes:c}=e.body;if(!s||!o||!a)return r.status(400).json({error:"First name, last name, and email are required"});let{data:m,error:p}=await l.from("customers").update({first_name:s,last_name:o,email:a,phone:n,phone_secondary:u,date_of_birth:i,address:d,notes:c,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(p)return console.error("Customer update error:",p),r.status(500).json({error:"Failed to update customer"});return r.status(200).json({customer:m})}{if("DELETE"!==e.method)return r.status(405).json({error:"Method not allowed"});if("Admin"!==a.role&&"DEV"!==a.role)return r.status(403).json({error:"Only admins can delete customers"});let{count:s}=await l.from("bookings").select("*",{count:"exact",head:!0}).eq("customer_id",t);if(s&&s>0)return r.status(400).json({error:"Cannot delete customer with existing bookings",message:`This customer has ${s} booking(s). Please cancel or delete all bookings first.`});let{error:o}=await l.from("customers").delete().eq("id",t);if(o)return console.error("Customer delete error:",o),r.status(500).json({error:"Failed to delete customer"});return r.status(200).json({message:"Customer deleted successfully"})}}catch(e){return console.error("Customer API error:",e),r.status(500).json({error:"Internal server error"})}}let m=(0,n.l)(s,"default"),p=(0,n.l)(s,"config"),f=new o.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/customers/[id]",pathname:"/api/admin/customers/[id]",bundlePath:"",filename:""},userland:s})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[805],()=>t(1089));module.exports=s})();