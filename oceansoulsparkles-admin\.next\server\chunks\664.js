exports.id=664,exports.ids=[664],exports.modules={6691:(e,t)=>{"use strict";var n,r;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{ACTION_FAST_REFRESH:function(){return i},ACTION_NAVIGATE:function(){return u},ACTION_PREFETCH:function(){return a},ACTION_REFRESH:function(){return o},ACTION_RESTORE:function(){return l},ACTION_SERVER_ACTION:function(){return c},ACTION_SERVER_PATCH:function(){return f},PrefetchCacheEntryStatus:function(){return r},PrefetchKind:function(){return n},isThenable:function(){return s}});let o="refresh",u="navigate",l="restore",f="server-patch",a="prefetch",i="fast-refresh",c="server-action";function s(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(n||(n={})),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4318:(e,t,n)=>{"use strict";function r(e,t,n,r){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return r}}),n(8364),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9577:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return _}});let r=n(167),o=n(997),u=r._(n(6689)),l=n(1401),f=n(2045),a=n(7420),i=n(7201),c=n(1443),s=n(5469),d=n(7443),p=n(2905),b=n(4318),y=n(953),v=n(6691);function h(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}let _=u.default.forwardRef(function(e,t){let n,r;let{href:a,as:_,children:O,prefetch:g=null,passHref:C,replace:j,shallow:E,scroll:T,locale:M,onClick:P,onMouseEnter:R,onTouchStart:m,legacyBehavior:x=!1,...A}=e;n=O,x&&("string"==typeof n||"number"==typeof n)&&(n=(0,o.jsx)("a",{children:n}));let I=u.default.useContext(s.RouterContext),S=u.default.useContext(d.AppRouterContext),L=null!=I?I:S,N=!I,k=!1!==g,U=null===g?v.PrefetchKind.AUTO:v.PrefetchKind.FULL,{href:K,as:F}=u.default.useMemo(()=>{if(!I){let e=h(a);return{href:e,as:_?h(_):e}}let[e,t]=(0,l.resolveHref)(I,a,!0);return{href:e,as:_?(0,l.resolveHref)(I,_):t||e}},[I,a,_]),H=u.default.useRef(K),w=u.default.useRef(F);x&&(r=u.default.Children.only(n));let D=x?r&&"object"==typeof r&&r.ref:t,[V,q,z]=(0,p.useIntersection)({rootMargin:"200px"}),B=u.default.useCallback(e=>{(w.current!==F||H.current!==K)&&(z(),w.current=F,H.current=K),V(e),D&&("function"==typeof D?D(e):"object"==typeof D&&(D.current=e))},[F,D,K,z,V]);u.default.useEffect(()=>{},[F,K,q,M,k,null==I?void 0:I.locale,L,N,U]);let G={ref:B,onClick(e){x||"function"!=typeof P||P(e),x&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&!e.defaultPrevented&&function(e,t,n,r,o,l,a,i,c){let{nodeName:s}=e.currentTarget;if("A"===s.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,f.isLocalURL)(n)))return;e.preventDefault();let d=()=>{let e=null==a||a;"beforePopState"in t?t[o?"replace":"push"](n,r,{shallow:l,locale:i,scroll:e}):t[o?"replace":"push"](r||n,{scroll:e})};c?u.default.startTransition(d):d()}(e,L,K,F,j,E,T,M,N)},onMouseEnter(e){x||"function"!=typeof R||R(e),x&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e)},onTouchStart:function(e){x||"function"!=typeof m||m(e),x&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e)}};if((0,i.isAbsoluteUrl)(F))G.href=F;else if(!x||C||"a"===r.type&&!("href"in r.props)){let e=void 0!==M?M:null==I?void 0:I.locale,t=(null==I?void 0:I.isLocaleDomain)&&(0,b.getDomainLocale)(F,e,null==I?void 0:I.locales,null==I?void 0:I.domainLocales);G.href=t||(0,y.addBasePath)((0,c.addLocale)(F,e,null==I?void 0:I.defaultLocale))}return x?u.default.cloneElement(r,G):(0,o.jsx)("a",{...A,...G,children:n})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2905:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return a}});let r=n(6689),o=n(3815),u="function"==typeof IntersectionObserver,l=new Map,f=[];function a(e){let{rootRef:t,rootMargin:n,disabled:a}=e,i=a||!u,[c,s]=(0,r.useState)(!1),d=(0,r.useRef)(null),p=(0,r.useCallback)(e=>{d.current=e},[]);return(0,r.useEffect)(()=>{if(u){if(i||c)return;let e=d.current;if(e&&e.tagName)return function(e,t,n){let{id:r,observer:o,elements:u}=function(e){let t;let n={root:e.root||null,margin:e.rootMargin||""},r=f.find(e=>e.root===n.root&&e.margin===n.margin);if(r&&(t=l.get(r)))return t;let o=new Map;return t={id:n,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)})},e),elements:o},f.push(n),l.set(n,t),t}(n);return u.set(e,t),o.observe(e),function(){if(u.delete(e),o.unobserve(e),0===u.size){o.disconnect(),l.delete(r);let e=f.findIndex(e=>e.root===r.root&&e.margin===r.margin);e>-1&&f.splice(e,1)}}}(e,e=>e&&s(e),{root:null==t?void 0:t.current,rootMargin:n})}else if(!c){let e=(0,o.requestIdleCallback)(()=>s(!0));return()=>(0,o.cancelIdleCallback)(e)}},[i,n,t,c,d.current]),[p,c,(0,r.useCallback)(()=>{s(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7443:(e,t,n)=>{"use strict";e.exports=n(7093).vendored.contexts.AppRouterContext},1664:(e,t,n)=>{e.exports=n(9577)}};