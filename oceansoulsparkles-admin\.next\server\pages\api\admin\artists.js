"use strict";(()=>{var a={};a.id=867,a.ids=[867],a.modules={2885:a=>{a.exports=require("@supabase/supabase-js")},145:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},8781:(a,e)=>{Object.defineProperty(e,"l",{enumerable:!0,get:function(){return function a(e,t){return t in e?e[t]:"then"in e&&"function"==typeof e.then?e.then(e=>a(e,t)):"function"==typeof e&&"default"===t?e:void 0}}})},5433:(a,e,t)=>{t.r(e),t.d(e,{config:()=>u,default:()=>d,routeModule:()=>c});var s={};t.r(s),t.d(s,{default:()=>o});var r=t(1802),i=t(7153),n=t(8781);let l=(0,t(2885).createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function o(a,e){let t=a.headers.authorization;if(!t||!t.startsWith("Bearer "))return e.status(401).json({error:"Unauthorized"});try{if("GET"===a.method){let{data:a,error:t}=await l.from("users").select("*").eq("role","artist");if(t&&t.message.includes('relation "users" does not exist'))return e.status(200).json({artists:[{id:1,name:"Sarah Johnson",email:"<EMAIL>",phone:"0412 345 678",specializations:["Box Braids","Cornrows","Twists"],status:"active",availability:{monday:{start:"09:00",end:"17:00",available:!0},tuesday:{start:"09:00",end:"17:00",available:!0},wednesday:{start:"09:00",end:"17:00",available:!0},thursday:{start:"09:00",end:"17:00",available:!0},friday:{start:"09:00",end:"17:00",available:!0},saturday:{start:"10:00",end:"16:00",available:!0},sunday:{start:"10:00",end:"16:00",available:!1}},rating:4.8,total_bookings:127,total_revenue:15240,created_at:"2024-01-15T00:00:00Z"},{id:2,name:"Maya Patel",email:"<EMAIL>",phone:"0423 456 789",specializations:["Locs","Faux Locs","Goddess Braids"],status:"active",availability:{monday:{start:"10:00",end:"18:00",available:!0},tuesday:{start:"10:00",end:"18:00",available:!0},wednesday:{start:"10:00",end:"18:00",available:!1},thursday:{start:"10:00",end:"18:00",available:!0},friday:{start:"10:00",end:"18:00",available:!0},saturday:{start:"09:00",end:"17:00",available:!0},sunday:{start:"11:00",end:"15:00",available:!0}},rating:4.9,total_bookings:98,total_revenue:12800,created_at:"2024-02-01T00:00:00Z"},{id:3,name:"Keisha Williams",email:"<EMAIL>",phone:"0434 567 890",specializations:["Senegalese Twists","Marley Twists","Passion Twists"],status:"active",availability:{monday:{start:"08:00",end:"16:00",available:!0},tuesday:{start:"08:00",end:"16:00",available:!0},wednesday:{start:"08:00",end:"16:00",available:!0},thursday:{start:"08:00",end:"16:00",available:!0},friday:{start:"08:00",end:"16:00",available:!0},saturday:{start:"09:00",end:"15:00",available:!0},sunday:{start:"09:00",end:"15:00",available:!1}},rating:4.7,total_bookings:145,total_revenue:18600,created_at:"2024-01-20T00:00:00Z"}],source:"mock"});if(t)throw t;return e.status(200).json({artists:a||[],source:"database"})}if("POST"===a.method){let{name:t,email:s,phone:r,specializations:i,availability:n}=a.body,{data:o,error:d}=await l.from("users").insert([{name:t,email:s,phone:r,role:"artist",specializations:i,availability:n,status:"active",created_at:new Date().toISOString()}]).select();if(d)throw d;return e.status(201).json({artist:o[0]})}return e.status(405).json({error:"Method not allowed"})}catch(a){return console.error("Artists API error:",a),e.status(500).json({error:"Internal server error"})}}let d=(0,n.l)(s,"default"),u=(0,n.l)(s,"config"),c=new r.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/artists",pathname:"/api/admin/artists",bundlePath:"",filename:""},userland:s})},7153:(a,e)=>{var t;Object.defineProperty(e,"x",{enumerable:!0,get:function(){return t}}),function(a){a.PAGES="PAGES",a.PAGES_API="PAGES_API",a.APP_PAGE="APP_PAGE",a.APP_ROUTE="APP_ROUTE"}(t||(t={}))},1802:(a,e,t)=>{a.exports=t(145)}};var e=require("../../../webpack-api-runtime.js");e.C(a);var t=e(e.s=5433);module.exports=t})();