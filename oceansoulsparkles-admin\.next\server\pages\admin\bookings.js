(()=>{var e={};e.id=92,e.ids=[92,660],e.modules={8704:e=>{e.exports={bookingsContainer:"Bookings_bookingsContainer__LxQns",header:"Bookings_header__LPpER",title:"Bookings_title__kkjQ_",headerActions:"Bookings_headerActions__oOFo9",newBookingBtn:"Bookings_newBookingBtn__DThjN",backButton:"Bookings_backButton__d7v8l",controlsPanel:"Bookings_controlsPanel__OqLxe",viewToggle:"Bookings_viewToggle__qHn5e",viewBtn:"Bookings_viewBtn__halWp",active:"Bookings_active__EPiR9",filters:"Bookings_filters__efSCE",searchInput:"Bookings_searchInput__WU9rK",statusFilter:"Bookings_statusFilter__cEsIO",dateFilter:"Bookings_dateFilter__91_jc",bookingsContent:"Bookings_bookingsContent__UNoyE",bookingsList:"Bookings_bookingsList__1shaa",emptyState:"Bookings_emptyState__vwGqK",bookingCard:"Bookings_bookingCard__rr_2I",bookingHeader:"Bookings_bookingHeader__kN4o8",customerInfo:"Bookings_customerInfo__sr86g",statusBadge:"Bookings_statusBadge__YyBWn",statusPending:"Bookings_statusPending__KxyNp",statusConfirmed:"Bookings_statusConfirmed__9YBLQ",statusCompleted:"Bookings_statusCompleted___ZbBM",statusCancelled:"Bookings_statusCancelled__L3mcN",statusDefault:"Bookings_statusDefault__P9Wkr",bookingDetails:"Bookings_bookingDetails__uGdps",serviceInfo:"Bookings_serviceInfo__aPhqp",timeInfo:"Bookings_timeInfo__fzguw",bookingNotes:"Bookings_bookingNotes__y0sB6",bookingActions:"Bookings_bookingActions__GQrrp",statusSelect:"Bookings_statusSelect__1MW5p",calendarView:"Bookings_calendarView__mzDJ0",comingSoon:"Bookings_comingSoon__Ssaev",loadingContainer:"Bookings_loadingContainer__aLt2y",loadingSpinner:"Bookings_loadingSpinner__V78_d",spin:"Bookings_spin__RJm_5"}},2671:(e,s,t)=>{"use strict";t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{config:()=>p,default:()=>u,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>_,reportWebVitals:()=>k,routeModule:()=>S,unstable_getServerProps:()=>B,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>x});var n=t(7093),o=t(5244),a=t(1323),r=t(2899),l=t.n(r),c=t(6814),d=t(9562),g=e([c,d]);[c,d]=g.then?(await g)():g;let u=(0,a.l)(d,"default"),_=(0,a.l)(d,"getStaticProps"),h=(0,a.l)(d,"getStaticPaths"),m=(0,a.l)(d,"getServerSideProps"),p=(0,a.l)(d,"config"),k=(0,a.l)(d,"reportWebVitals"),x=(0,a.l)(d,"unstable_getStaticProps"),v=(0,a.l)(d,"unstable_getStaticPaths"),j=(0,a.l)(d,"unstable_getStaticParams"),B=(0,a.l)(d,"unstable_getServerProps"),b=(0,a.l)(d,"unstable_getServerSideProps"),S=new n.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/admin/bookings",pathname:"/admin/bookings",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});i()}catch(e){i(e)}})},9562:(e,s,t)=>{"use strict";t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{default:()=>p});var n=t(997),o=t(6689),a=t(968),r=t.n(a),l=t(1163),c=t(1664),d=t.n(c),g=t(4845),u=t(8568),_=t(8704),h=t.n(_),m=e([g]);function p(){let e=(0,l.useRouter)(),{user:s,loading:t}=(0,u.a)(),[i,a]=(0,o.useState)([]),[c,_]=(0,o.useState)([]),[m,p]=(0,o.useState)(!0),[k,x]=(0,o.useState)(new Date().toISOString().split("T")[0]),[v,j]=(0,o.useState)("all"),[B,b]=(0,o.useState)(""),[S,f]=(0,o.useState)("list"),N=async(e,s)=>{try{let t=localStorage.getItem("admin-token");if(!(await fetch(`/api/admin/bookings/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({status:s})})).ok)throw Error("Failed to update booking status");a(t=>t.map(t=>t.id===e?{...t,status:s}:t))}catch(e){console.error("Error updating booking status:",e)}},C=e=>{switch(e){case"confirmed":return h().statusConfirmed;case"pending":return h().statusPending;case"completed":return h().statusCompleted;case"cancelled":return h().statusCancelled;default:return h().statusDefault}},w=e=>{let s=new Date(e);return{date:s.toLocaleDateString(),time:s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}};return t||m?n.jsx(g.Z,{children:(0,n.jsxs)("div",{className:h().loadingContainer,children:[n.jsx("div",{className:h().loadingSpinner}),n.jsx("p",{children:"Loading bookings..."})]})}):(0,n.jsxs)(g.Z,{children:[(0,n.jsxs)(r(),{children:[n.jsx("title",{children:"Bookings Management | Ocean Soul Sparkles Admin"}),n.jsx("meta",{name:"description",content:"Manage customer bookings and appointments"})]}),(0,n.jsxs)("div",{className:h().bookingsContainer,children:[(0,n.jsxs)("header",{className:h().header,children:[n.jsx("h1",{className:h().title,children:"Bookings Management"}),(0,n.jsxs)("div",{className:h().headerActions,children:[n.jsx(d(),{href:"/admin/bookings/new",className:h().newBookingBtn,children:"+ New Booking"}),n.jsx("button",{className:h().backButton,onClick:()=>e.push("/"),children:"← Back to Dashboard"})]})]}),(0,n.jsxs)("div",{className:h().controlsPanel,children:[(0,n.jsxs)("div",{className:h().viewToggle,children:[n.jsx("button",{className:`${h().viewBtn} ${"list"===S?h().active:""}`,onClick:()=>f("list"),children:"List View"}),n.jsx("button",{className:`${h().viewBtn} ${"calendar"===S?h().active:""}`,onClick:()=>f("calendar"),children:"Calendar View"})]}),(0,n.jsxs)("div",{className:h().filters,children:[n.jsx("input",{type:"text",placeholder:"Search bookings...",value:B,onChange:e=>b(e.target.value),className:h().searchInput}),(0,n.jsxs)("select",{value:v,onChange:e=>j(e.target.value),className:h().statusFilter,children:[n.jsx("option",{value:"all",children:"All Status"}),n.jsx("option",{value:"pending",children:"Pending"}),n.jsx("option",{value:"confirmed",children:"Confirmed"}),n.jsx("option",{value:"completed",children:"Completed"}),n.jsx("option",{value:"cancelled",children:"Cancelled"})]}),"list"===S&&n.jsx("input",{type:"date",value:k,onChange:e=>x(e.target.value),className:h().dateFilter})]})]}),(0,n.jsxs)("div",{className:h().bookingsContent,children:["list"===S?n.jsx("div",{className:h().bookingsList,children:0===c.length?n.jsx("div",{className:h().emptyState,children:n.jsx("p",{children:"No bookings found for the selected criteria."})}):c.map(e=>{let{date:s,time:t}=w(e.start_time),i=w(e.end_time).time;return(0,n.jsxs)("div",{className:h().bookingCard,children:[(0,n.jsxs)("div",{className:h().bookingHeader,children:[(0,n.jsxs)("div",{className:h().customerInfo,children:[n.jsx("h3",{children:e.customer_name}),n.jsx("p",{children:e.customer_email})]}),n.jsx("div",{className:`${h().statusBadge} ${C(e.status)}`,children:e.status})]}),(0,n.jsxs)("div",{className:h().bookingDetails,children:[(0,n.jsxs)("div",{className:h().serviceInfo,children:[n.jsx("strong",{children:e.service_name}),(0,n.jsxs)("p",{children:["Artist: ",e.artist]}),(0,n.jsxs)("p",{children:["Price: $",e.price]})]}),(0,n.jsxs)("div",{className:h().timeInfo,children:[n.jsx("p",{children:n.jsx("strong",{children:s})}),(0,n.jsxs)("p",{children:[t," - ",i]})]})]}),e.notes&&(0,n.jsxs)("div",{className:h().bookingNotes,children:[n.jsx("strong",{children:"Notes:"})," ",e.notes]}),(0,n.jsxs)("div",{className:h().bookingActions,children:[n.jsx(d(),{href:`/admin/bookings/${e.id}`,className:h().viewBtn,children:"View Details"}),(0,n.jsxs)("select",{value:e.status,onChange:s=>N(e.id,s.target.value),className:h().statusSelect,children:[n.jsx("option",{value:"pending",children:"Pending"}),n.jsx("option",{value:"confirmed",children:"Confirmed"}),n.jsx("option",{value:"completed",children:"Completed"}),n.jsx("option",{value:"cancelled",children:"Cancelled"})]})]})]},e.id)})}):n.jsx("div",{className:h().calendarView,children:(0,n.jsxs)("div",{className:h().comingSoon,children:[n.jsx("h3",{children:"Calendar View"}),n.jsx("p",{children:"Calendar view will be implemented here with full booking visualization."}),n.jsx("p",{children:"Features will include:"}),(0,n.jsxs)("ul",{children:[n.jsx("li",{children:"Monthly/weekly/daily calendar views"}),n.jsx("li",{children:"Drag and drop booking management"}),n.jsx("li",{children:"Artist availability overlay"}),n.jsx("li",{children:"Time slot management"}),n.jsx("li",{children:"Booking conflict detection"})]})]})}),"        "]})]})]})}g=(m.then?(await m)():m)[0],i()}catch(e){i(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[899,212,664,441],()=>t(2671));module.exports=i})();