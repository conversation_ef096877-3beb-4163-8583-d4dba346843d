(()=>{var e={};e.id=473,e.ids=[473,660],e.modules={8154:e=>{e.exports={serviceFormContainer:"ServiceForm_serviceFormContainer__x_rm_",header:"ServiceForm_header__MJoC5",breadcrumb:"ServiceForm_breadcrumb__Sujex",backButton:"ServiceForm_backButton__qvGeh",formContent:"ServiceForm_formContent__7hqCc",form:"ServiceForm_form__y0s41",errorAlert:"ServiceForm_errorAlert__zu0M_",formGrid:"ServiceForm_formGrid__z4a_v",formGroup:"ServiceForm_formGroup__bBtrC",inputError:"ServiceForm_inputError__4HD91",errorText:"ServiceForm_errorText__3nMcX",visibilitySection:"ServiceForm_visibilitySection__L70gF",checkboxGroup:"ServiceForm_checkboxGroup__HHCB0",checkboxLabel:"ServiceForm_checkboxLabel__dHq0_",formActions:"ServiceForm_formActions__RKZe1",submitButton:"ServiceForm_submitButton__kH7U0",cancelButton:"ServiceForm_cancelButton__eloNY",loadingContainer:"ServiceForm_loadingContainer__OdLd7",errorContainer:"ServiceForm_errorContainer__Z68Z1",notFoundContainer:"ServiceForm_notFoundContainer__bRMUf",loadingSpinner:"ServiceForm_loadingSpinner__wb_pk",spin:"ServiceForm_spin__VG_ya"}},5137:(e,r,i)=>{"use strict";i.a(e,async(e,s)=>{try{i.r(r),i.d(r,{config:()=>x,default:()=>u,getServerSideProps:()=>v,getStaticPaths:()=>p,getStaticProps:()=>h,reportWebVitals:()=>_,routeModule:()=>N,unstable_getServerProps:()=>g,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>b});var t=i(7093),a=i(5244),n=i(1323),c=i(2899),o=i.n(c),l=i(6814),d=i(7422),m=e([l,d]);[l,d]=m.then?(await m)():m;let u=(0,n.l)(d,"default"),h=(0,n.l)(d,"getStaticProps"),p=(0,n.l)(d,"getStaticPaths"),v=(0,n.l)(d,"getServerSideProps"),x=(0,n.l)(d,"config"),_=(0,n.l)(d,"reportWebVitals"),b=(0,n.l)(d,"unstable_getStaticProps"),j=(0,n.l)(d,"unstable_getStaticPaths"),S=(0,n.l)(d,"unstable_getStaticParams"),g=(0,n.l)(d,"unstable_getServerProps"),f=(0,n.l)(d,"unstable_getServerSideProps"),N=new t.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/admin/services/[id]/edit",pathname:"/admin/services/[id]/edit",bundlePath:"",filename:""},components:{App:l.default,Document:o()},userland:d});s()}catch(e){s(e)}})},7422:(e,r,i)=>{"use strict";i.a(e,async(e,s)=>{try{i.r(r),i.d(r,{default:()=>x});var t=i(997),a=i(6689),n=i(1163),c=i(968),o=i.n(c),l=i(1664),d=i.n(l),m=i(8568),u=i(4845),h=i(8154),p=i.n(h),v=e([u]);function x(){let e=(0,n.useRouter)(),{id:r}=e.query,{user:i,loading:s}=(0,m.a)(),[c,l]=(0,a.useState)(!0),[h,v]=(0,a.useState)(!1),[x,_]=(0,a.useState)(null),[b,j]=(0,a.useState)({name:"",description:"",duration:"",price:"",category:"Hair Braiding",status:"active",visible_on_public:!0,visible_on_pos:!0,visible_on_events:!0}),[S,g]=(0,a.useState)({}),f=e=>{let{name:r,value:i,type:s,checked:t}=e.target;j(e=>({...e,[r]:"checkbox"===s?t:i})),S[r]&&g(e=>({...e,[r]:null}))},N=()=>{let e={};return b.name.trim()||(e.name="Service name is required"),b.category||(e.category="Category is required"),b.duration&&(isNaN(b.duration)||0>=parseInt(b.duration))&&(e.duration="Duration must be a positive number"),b.price&&(isNaN(b.price)||0>parseFloat(b.price))&&(e.price="Price must be a valid number"),g(e),0===Object.keys(e).length},y=async i=>{if(i.preventDefault(),N())try{v(!0);let i=localStorage.getItem("admin-token"),s=await fetch(`/api/admin/services/${r}`,{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify(b)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update service")}e.push(`/admin/services/${r}`)}catch(e){console.error("Error updating service:",e),g({submit:e.message})}finally{v(!1)}};return s||c?t.jsx(u.Z,{children:(0,t.jsxs)("div",{className:p().loadingContainer,children:[t.jsx("div",{className:p().loadingSpinner}),t.jsx("p",{children:"Loading service details..."})]})}):S.load?t.jsx(u.Z,{children:(0,t.jsxs)("div",{className:p().errorContainer,children:[t.jsx("h2",{children:"Error Loading Service"}),t.jsx("p",{children:S.load}),t.jsx(d(),{href:"/admin/services",className:p().backButton,children:"← Back to Services"})]})}):x?(0,t.jsxs)(u.Z,{children:[(0,t.jsxs)(o(),{children:[(0,t.jsxs)("title",{children:["Edit ",x.name," | Ocean Soul Sparkles Admin"]}),t.jsx("meta",{name:"description",content:`Edit ${x.name} service`})]}),(0,t.jsxs)("div",{className:p().serviceFormContainer,children:[(0,t.jsxs)("header",{className:p().header,children:[(0,t.jsxs)("div",{className:p().breadcrumb,children:[t.jsx(d(),{href:"/admin/services",children:"Services"}),t.jsx("span",{children:"/"}),t.jsx(d(),{href:`/admin/services/${x.id}`,children:x.name}),t.jsx("span",{children:"/"}),t.jsx("span",{children:"Edit"})]}),t.jsx(d(),{href:`/admin/services/${x.id}`,className:p().backButton,children:"← Back to Service"})]}),(0,t.jsxs)("div",{className:p().formContent,children:[(0,t.jsxs)("h1",{children:["Edit Service: ",x.name]}),(0,t.jsxs)("form",{onSubmit:y,className:p().form,children:[S.submit&&t.jsx("div",{className:p().errorAlert,children:S.submit}),(0,t.jsxs)("div",{className:p().formGrid,children:[(0,t.jsxs)("div",{className:p().formGroup,children:[t.jsx("label",{htmlFor:"name",children:"Service Name *"}),t.jsx("input",{type:"text",id:"name",name:"name",value:b.name,onChange:f,className:S.name?p().inputError:"",placeholder:"Enter service name",required:!0}),S.name&&t.jsx("span",{className:p().errorText,children:S.name})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[t.jsx("label",{htmlFor:"category",children:"Category *"}),t.jsx("select",{id:"category",name:"category",value:b.category,onChange:f,className:S.category?p().inputError:"",required:!0,children:["Hair Braiding","Protective Styles","Hair Care","Styling","Consultation","Special Events","Maintenance"].map(e=>t.jsx("option",{value:e,children:e},e))}),S.category&&t.jsx("span",{className:p().errorText,children:S.category})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[t.jsx("label",{htmlFor:"duration",children:"Duration (minutes)"}),t.jsx("input",{type:"number",id:"duration",name:"duration",value:b.duration,onChange:f,className:S.duration?p().inputError:"",placeholder:"e.g., 120",min:"1"}),S.duration&&t.jsx("span",{className:p().errorText,children:S.duration})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[t.jsx("label",{htmlFor:"price",children:"Price (AUD)"}),t.jsx("input",{type:"number",id:"price",name:"price",value:b.price,onChange:f,className:S.price?p().inputError:"",placeholder:"e.g., 150.00",min:"0",step:"0.01"}),S.price&&t.jsx("span",{className:p().errorText,children:S.price})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[t.jsx("label",{htmlFor:"status",children:"Status"}),(0,t.jsxs)("select",{id:"status",name:"status",value:b.status,onChange:f,children:[t.jsx("option",{value:"active",children:"Active"}),t.jsx("option",{value:"inactive",children:"Inactive"}),t.jsx("option",{value:"draft",children:"Draft"})]})]})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[t.jsx("label",{htmlFor:"description",children:"Description"}),t.jsx("textarea",{id:"description",name:"description",value:b.description,onChange:f,rows:"4",placeholder:"Describe the service, what's included, any special requirements..."})]}),(0,t.jsxs)("div",{className:p().visibilitySection,children:[t.jsx("h3",{children:"Visibility Options"}),(0,t.jsxs)("div",{className:p().checkboxGroup,children:[(0,t.jsxs)("label",{className:p().checkboxLabel,children:[t.jsx("input",{type:"checkbox",name:"visible_on_public",checked:b.visible_on_public,onChange:f}),t.jsx("span",{children:"Show on public website"})]}),(0,t.jsxs)("label",{className:p().checkboxLabel,children:[t.jsx("input",{type:"checkbox",name:"visible_on_pos",checked:b.visible_on_pos,onChange:f}),t.jsx("span",{children:"Available in POS system"})]}),(0,t.jsxs)("label",{className:p().checkboxLabel,children:[t.jsx("input",{type:"checkbox",name:"visible_on_events",checked:b.visible_on_events,onChange:f}),t.jsx("span",{children:"Available for events"})]})]})]}),(0,t.jsxs)("div",{className:p().formActions,children:[t.jsx("button",{type:"submit",className:p().submitButton,disabled:h,children:h?"Saving...":"Save Changes"}),t.jsx(d(),{href:`/admin/services/${x.id}`,className:p().cancelButton,children:"Cancel"})]})]})]})]})]}):t.jsx(u.Z,{children:(0,t.jsxs)("div",{className:p().notFoundContainer,children:[t.jsx("h2",{children:"Service Not Found"}),t.jsx(d(),{href:"/admin/services",className:p().backButton,children:"← Back to Services"})]})})}u=(v.then?(await v)():v)[0],s()}catch(e){s(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var r=require("../../../../webpack-runtime.js");r.C(e);var i=e=>r(r.s=e),s=r.X(0,[899,212,664,441],()=>i(5137));module.exports=s})();