{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/login", "destination": "/admin/login", "statusCode": 307, "regex": "^(?!/_next)/login(?:/)?$"}, {"source": "/shop", "destination": "/", "statusCode": 307, "regex": "^(?!/_next)/shop(?:/)?$"}, {"source": "/book-online", "destination": "/admin/bookings", "statusCode": 307, "regex": "^(?!/_next)/book-online(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(self), microphone=(self), geolocation=(self), payment=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://cdn.onesignal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://js.squareup.com https://api.onesignal.com wss://*.supabase.co; frame-src 'self' https://js.squareup.com;"}, {"key": "X-Admin-Portal", "value": "true"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, private"}, {"key": "X-Admin-API", "value": "true"}], "regex": "^/api(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/admin/bookings/[id]", "regex": "^/admin/bookings/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/bookings/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/customers/[id]", "regex": "^/admin/customers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/customers/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/services/[id]", "regex": "^/admin/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/services/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/services/[id]/edit", "regex": "^/admin/services/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/services/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/api/admin/bookings/[id]", "regex": "^/api/admin/bookings/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/bookings/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/customers/[id]", "regex": "^/api/admin/customers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/customers/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/customers/[id]/bookings", "regex": "^/api/admin/customers/([^/]+?)/bookings(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/customers/(?<nxtPid>[^/]+?)/bookings(?:/)?$"}, {"page": "/api/admin/services/[id]", "regex": "^/api/admin/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/services/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/admin/artists", "regex": "^/admin/artists(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/artists(?:/)?$"}, {"page": "/admin/bookings", "regex": "^/admin/bookings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/bookings(?:/)?$"}, {"page": "/admin/bookings/new", "regex": "^/admin/bookings/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/bookings/new(?:/)?$"}, {"page": "/admin/customers", "regex": "^/admin/customers(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/customers(?:/)?$"}, {"page": "/admin/customers/new", "regex": "^/admin/customers/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/customers/new(?:/)?$"}, {"page": "/admin/customers-new", "regex": "^/admin/customers\\-new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/customers\\-new(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/inventory", "regex": "^/admin/inventory(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/inventory(?:/)?$"}, {"page": "/admin/login", "regex": "^/admin/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/login(?:/)?$"}, {"page": "/admin/pos", "regex": "^/admin/pos(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/pos(?:/)?$"}, {"page": "/admin/products", "regex": "^/admin/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products(?:/)?$"}, {"page": "/admin/services", "regex": "^/admin/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/services(?:/)?$"}, {"page": "/admin/services/new", "regex": "^/admin/services/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/services/new(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}