"use strict";(()=>{var e={};e.id=397,e.ids=[397],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},368:(e,t,r)=>{r.r(t),r.d(t,{config:()=>l,default:()=>d,routeModule:()=>c});var o={};r.r(o),r.d(o,{default:()=>u});var n=r(1802),i=r(7153),s=r(8781),a=r(8456),m=r(7474);async function u(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});let r=Math.random().toString(36).substring(2,8);console.log(`[${r}] POS booking creation API called`);try{let{user:o,error:n}=await (0,m.authenticateAdminRequest)(e);if(n||!o)return t.status(401).json({error:"Authentication required",message:n?.message||"Authentication failed",requestId:r});console.log(`[${r}] Authentication successful. User: ${o?.email}`);let{service:i,artist:s,tier:u,timeSlot:d,customer:l,payment:c}=e.body;if(!i?.id||!s?.id||!u?.id||!d?.time||!l||!c)return t.status(400).json({error:"Missing required data",message:"Service, artist, tier, timeSlot, customer, and payment information are required",requestId:r});if("string"!=typeof i.id||""===i.id.trim()||"string"!=typeof s.id||""===s.id.trim()||"string"!=typeof u.id||""===u.id.trim())return t.status(400).json({error:"Invalid input",message:"Service ID, Artist ID, and Tier ID must be non-empty strings.",requestId:r});if("number"!=typeof u.duration||u.duration<=0)return t.status(400).json({error:"Invalid input",message:"Tier duration must be a positive number.",requestId:r});if("number"!=typeof u.price||u.price<0)return t.status(400).json({error:"Invalid input",message:"Tier price must be a non-negative number.",requestId:r});if(!d.time||"string"!=typeof d.time||isNaN(new Date(d.time).getTime()))return t.status(400).json({error:"Invalid input",message:"Invalid timeSlot.time format. Expected valid ISO string.",requestId:r});if(l.email&&("string"!=typeof l.email||!l.email.includes("@")||!l.email.includes(".")))return t.status(400).json({error:"Invalid input",message:"Invalid customer email format.",requestId:r});if(!l.isAnonymous&&(!l.name||"string"!=typeof l.name||""===l.name.trim()))return t.status(400).json({error:"Invalid input",message:"Customer name is required for non-anonymous customers.",requestId:r});if("number"!=typeof c.amount||c.amount<0)return t.status(400).json({error:"Invalid input",message:"Payment amount must be a non-negative number.",requestId:r});if("string"!=typeof c.method||""===c.method.trim())return t.status(400).json({error:"Invalid input",message:"Payment method must be a non-empty string.",requestId:r});console.log(`[${r}] Creating POS booking for service: ${i.name}, artist: ${s.name}`);let p=null,g=null,f=null;try{let e=`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`;if(l.isAnonymous)try{let{data:e,error:t}=await a.pR.from("customers").insert([{name:l.name||`Walk-in Customer #${Date.now()}`,email:null,phone:l.phone||null,notes:"POS anonymous customer",created_via:"pos"}]).select().single();if(t)throw console.error(`[${r}] Anonymous customer creation failed:`,t),Error(`Anonymous customer creation failed: ${t.message}`);p=e.id,console.log(`[${r}] Created anonymous customer: ${p}`)}catch(e){throw console.error(`[${r}] Anonymous customer creation failed:`,e),Error(`Anonymous customer creation failed: ${e.message}`)}else try{let{data:e,error:t}=await a.pR.from("customers").insert([{name:l.name,email:l.email||null,phone:l.phone||null,notes:l.notes||"POS customer",created_via:"pos"}]).select().single();if(t)throw console.error(`[${r}] Named customer creation failed:`,t),Error(`Named customer creation failed: ${t.message}`);p=e.id,console.log(`[${r}] Created new customer: ${p} (${l.email||"no email"})`)}catch(e){throw console.error(`[${r}] Named customer creation failed:`,e),Error(`Customer creation failed: ${e.message}`)}let o=new Date(d.time),n=new Date(o.getTime()+6e4*u.duration);try{let{data:t,error:m}=await a.pR.from("bookings").insert([{customer_id:p,service_id:i.id,artist_id:s.id,assigned_artist_id:s.id,start_time:o.toISOString(),end_time:n.toISOString(),status:"confirmed",location:"POS Terminal",notes:`POS booking - ${u.name} tier - ${c.method} payment - Artist: ${s.name}`,booking_source:"pos",pos_session_id:e,tier_name:u.name,tier_price:u.price,total_amount:c.amount}]).select().single();if(m)throw console.error(`[${r}] Error creating booking:`,m),Error(`Failed to create booking: ${m.message}`);g=t.id,console.log(`[${r}] Created POS booking: ${g} for session: ${e}`)}catch(e){throw console.error(`[${r}] Booking creation failed:`,e),Error(`Booking creation failed: ${e.message}`)}try{let{data:t,error:o}=await a.pR.from("payments").insert([{booking_id:g,amount:c.amount,currency:c.currency||"AUD",payment_method:c.method,payment_status:"completed",transaction_id:c.details?.transactionId||null,payment_date:new Date().toISOString(),notes:`POS terminal payment - ${i.name} (${u.name})`,pos_session_id:e,square_payment_id:c.details?.paymentId||null,receipt_url:c.details?.receiptUrl||null}]).select().single();if(o)throw console.error(`[${r}] Error creating payment:`,o),Error(`Failed to create payment record: ${o.message}`);f=t.id,console.log(`[${r}] Created POS payment: ${f} for session: ${e}`)}catch(e){throw console.error(`[${r}] Payment creation failed:`,e),Error(`Payment creation failed: ${e.message}`)}return t.status(201).json({success:!0,booking:{id:g,customer_id:p,service_id:i.id,start_time:o.toISOString(),end_time:n.toISOString(),status:"confirmed"},payment:{id:f,amount:c.amount,currency:c.currency||"AUD",method:c.method,status:"completed"},customer:{id:p,name:l.name,email:l.email||null},receipt:{service:i.name,tier:u.name,duration:u.duration,amount:c.amount,currency:c.currency||"AUD",payment_method:c.method,booking_time:o.toISOString(),booking_id:g}})}catch(e){throw console.error(`[${r}] Transaction error:`,e),f&&await a.pR.from("payments").delete().eq("id",f),g&&await a.pR.from("bookings").delete().eq("id",g),p&&l.isAnonymous&&await a.pR.from("customers").delete().eq("id",p),e}}catch(n){console.error(`[${r}] POS Booking Creation Error:`,n);let e=500,o="Failed to create booking";return n.message&&n.message.includes("validation")?(e=400,o=n.message):n.message&&n.message.includes("not found")?(e=404,o=n.message):n.message&&(o=n.message),t.status(e).json({error:"Booking creation failed",message:o,requestId:r})}}let d=(0,s.l)(o,"default"),l=(0,s.l)(o,"config"),c=new n.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/pos/create-booking",pathname:"/api/admin/pos/create-booking",bundlePath:"",filename:""},userland:o})},8456:(e,t,r)=>{r.d(t,{pR:()=>a});var o=r(2885);let n="https://ndlgbcsbidyhxbpqzgqp.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",s=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!n||!i)throw Error("Missing Supabase environment variables");(0,o.createClient)(n,i,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let a=(0,o.createClient)(n,s||i,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[805],()=>r(368));module.exports=o})();