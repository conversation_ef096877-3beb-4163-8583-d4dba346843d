import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    // Only admin and dev can access all customers
    if (user.role !== 'Admin' && user.role !== 'DEV') {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    // Get customers with booking count
    const { data: customers, error } = await supabase
      .from('customers')
      .select(`
        id,
        first_name,
        last_name,
        email,
        phone,
        date_of_birth,
        address,
        emergency_contact_name,
        emergency_contact_phone,
        notes,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Customers query error:', error);
      return res.status(500).json({ error: 'Failed to fetch customers' });
    }

    // Get booking counts for each customer
    const customerIds = customers?.map(c => c.id) || [];
    const { data: bookingCounts } = await supabase
      .from('bookings')
      .select('customer_id')
      .in('customer_id', customerIds);

    // Count bookings per customer
    const bookingCountMap = (bookingCounts || []).reduce((acc, booking) => {
      acc[booking.customer_id] = (acc[booking.customer_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Transform data
    const transformedCustomers = (customers || []).map(customer => ({
      id: customer.id,
      name: `${customer.first_name} ${customer.last_name}`,
      first_name: customer.first_name,
      last_name: customer.last_name,
      email: customer.email,
      phone: customer.phone,
      date_of_birth: customer.date_of_birth,
      address: customer.address,
      emergency_contact_name: customer.emergency_contact_name,
      emergency_contact_phone: customer.emergency_contact_phone,
      notes: customer.notes,
      total_bookings: bookingCountMap[customer.id] || 0,
      created_at: customer.created_at,
      updated_at: customer.updated_at
    }));

    return res.status(200).json({
      customers: transformedCustomers,
      total: transformedCustomers.length
    });

  } catch (error) {
    console.error('Customers API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
