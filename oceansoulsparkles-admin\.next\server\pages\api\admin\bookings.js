"use strict";(()=>{var e={};e.id=424,e.ids=[424],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},5948:(e,t,r)=>{r.r(t),r.d(t,{config:()=>c,default:()=>_,routeModule:()=>p});var s={};r.r(s),r.d(s,{default:()=>m});var a=r(1802),i=r(7153),o=r(8781),n=r(7474),d=r(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,d.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function m(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!r)return t.status(401).json({error:"No authentication token"});let s=await (0,n.Wg)(r);if(!s.valid||!s.user)return t.status(401).json({error:"Invalid authentication"});let a=s.user,i=l.from("bookings").select(`
        id,
        booking_date,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        created_at,
        customer_id,
        service_id,
        assigned_artist_id,
        customers (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        services (
          id,
          name,
          duration,
          price
        ),
        artist_profiles!assigned_artist_id (
          id,
          artist_name,
          display_name
        )
      `).order("booking_date",{ascending:!1}).order("start_time",{ascending:!1});("Artist"===a.role||"Braider"===a.role)&&(i=i.eq("assigned_artist_id",a.id));let{data:o,error:d}=await i;if(d)return console.error("Bookings query error:",d),t.status(500).json({error:"Failed to fetch bookings"});let u=(o||[]).map(e=>{let t=Array.isArray(e.customers)?e.customers[0]:e.customers,r=Array.isArray(e.services)?e.services[0]:e.services,s=Array.isArray(e.artist_profiles)?e.artist_profiles[0]:e.artist_profiles,a=new Date(e.start_time).toTimeString().slice(0,5);return{id:e.id,customer_name:t?`${t.first_name} ${t.last_name}`:"Unknown Customer",customer_email:t?.email||"",customer_phone:t?.phone||"",service_name:r?.name||"Unknown Service",service_duration:r?.duration||0,service_price:r?.price||0,artist_name:s?.artist_name||s?.display_name||"Unassigned",booking_date:e.booking_date,booking_time:a,start_time:e.start_time,end_time:e.end_time,status:e.status,total_amount:e.total_amount,notes:e.notes,created_at:e.created_at,customers:t,services:r,artist_profiles:s}});return t.status(200).json({bookings:u,total:u.length})}catch(e){return console.error("Bookings API error:",e),t.status(500).json({error:"Internal server error"})}}let _=(0,o.l)(s,"default"),c=(0,o.l)(s,"config"),p=new a.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/bookings",pathname:"/api/admin/bookings",bundlePath:"",filename:""},userland:s})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[805],()=>r(5948));module.exports=s})();