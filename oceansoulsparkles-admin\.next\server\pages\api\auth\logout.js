"use strict";(()=>{var e={};e.id=845,e.ids=[845],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},6270:(e,t,r)=>{r.r(t),r.d(t,{config:()=>l,default:()=>d,routeModule:()=>c});var a={};r.r(a),r.d(a,{default:()=>n});var s=r(1802),o=r(7153),i=r(8781),u=r(7474);async function n(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=e.cookies["admin-token"]||e.headers.authorization?.replace("Bearer ","");if(r){let t=await (0,u.Wg)(r);if(t.valid&&t.user){let r=e.headers["x-forwarded-for"]||e.headers["x-real-ip"]||e.connection.remoteAddress||"unknown";await (0,u.rE)(t.user.id,r)}}return t.setHeader("Set-Cookie",["admin-token=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0"]),t.status(200).json({success:!0,message:"Logged out successfully"})}catch(e){return console.error("Logout API error:",e),t.setHeader("Set-Cookie",["admin-token=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0"]),t.status(200).json({success:!0,message:"Logged out"})}}let d=(0,i.l)(a,"default"),l=(0,i.l)(a,"config"),c=new s.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/auth/logout",pathname:"/api/auth/logout",bundlePath:"",filename:""},userland:a})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[805],()=>r(6270));module.exports=a})();