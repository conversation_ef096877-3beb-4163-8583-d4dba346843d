(()=>{var t={};t.id=291,t.ids=[291,660],t.modules={3680:t=>{t.exports={artistsContainer:"Artists_artistsContainer__0rRfo",header:"Artists_header__l056z",title:"Artists_title__vjxcu",headerActions:"Artists_headerActions__90PqJ",newArtistBtn:"Artists_newArtistBtn__bzTM4",backButton:"Artists_backButton__4_qDa",controlsPanel:"Artists_controlsPanel__hiH1g",searchSection:"Artists_searchSection__YNGbF",searchInput:"Artists_searchInput__L5n_I",filters:"Artists_filters__brZMt",specializationFilter:"Artists_specializationFilter__pdB2f",statusFilter:"Artists_statusFilter__NXaRr",sortSelect:"Artists_sortSelect__Mo0wU",artistsContent:"Artists_artistsContent__oRUhp",statsCards:"Artists_statsCards__szEZ5",statCard:"Artists_statCard__fzXfl",statValue:"Artists_statValue__yQNVC",emptyState:"Artists_emptyState__quJ1M",artistsGrid:"Artists_artistsGrid__gpdVz",artistCard:"Artists_artistCard__BAcxg",artistHeader:"Artists_artistHeader__NZbK_",artistInfo:"Artists_artistInfo__GEQEA",badges:"Artists_badges__eZJwh",statusBadge:"Artists_statusBadge__o_ZJI",availabilityBadge:"Artists_availabilityBadge__WHNQb",statusActive:"Artists_statusActive__Xr6co",statusInactive:"Artists_statusInactive__HVOhk",statusDefault:"Artists_statusDefault__BVw_v",availabilityAvailable:"Artists_availabilityAvailable__XSsWU",availabilityBusy:"Artists_availabilityBusy__fXExt",availabilityUnavailable:"Artists_availabilityUnavailable__LIv9A",availabilityDefault:"Artists_availabilityDefault__9jx5n",specializations:"Artists_specializations__bGPbn",specializationTags:"Artists_specializationTags__2x8sm",specializationTag:"Artists_specializationTag__nPNET",artistStats:"Artists_artistStats__VqwDN",statItem:"Artists_statItem__PrhZt",statLabel:"Artists_statLabel__UcjBT",artistActions:"Artists_artistActions__dYGhw",viewBtn:"Artists_viewBtn__LP0kP",toggleBtn:"Artists_toggleBtn__HJwor",activate:"Artists_activate__tWij9",deactivate:"Artists_deactivate__OU5kr",loadingContainer:"Artists_loadingContainer__Jck0K",loadingSpinner:"Artists_loadingSpinner__DTj7G",spin:"Artists_spin__RDcmV"}},1314:(t,s,a)=>{"use strict";a.a(t,async(t,e)=>{try{a.r(s),a.d(s,{config:()=>m,default:()=>u,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>v,reportWebVitals:()=>p,routeModule:()=>S,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>A,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>g});var i=a(7093),r=a(5244),l=a(1323),n=a(2899),c=a.n(n),d=a(6814),o=a(8160),_=t([d,o]);[d,o]=_.then?(await _)():_;let u=(0,l.l)(o,"default"),v=(0,l.l)(o,"getStaticProps"),h=(0,l.l)(o,"getStaticPaths"),x=(0,l.l)(o,"getServerSideProps"),m=(0,l.l)(o,"config"),p=(0,l.l)(o,"reportWebVitals"),g=(0,l.l)(o,"unstable_getStaticProps"),j=(0,l.l)(o,"unstable_getStaticPaths"),A=(0,l.l)(o,"unstable_getStaticParams"),b=(0,l.l)(o,"unstable_getServerProps"),N=(0,l.l)(o,"unstable_getServerSideProps"),S=new i.PagesRouteModule({definition:{kind:r.x.PAGES,page:"/admin/artists",pathname:"/admin/artists",bundlePath:"",filename:""},components:{App:d.default,Document:c()},userland:o});e()}catch(t){e(t)}})},8160:(t,s,a)=>{"use strict";a.a(t,async(t,e)=>{try{a.r(s),a.d(s,{default:()=>x});var i=a(997),r=a(6689),l=a(968),n=a.n(l);a(1163);var c=a(1664),d=a.n(c),o=a(8568),_=a(4845),u=a(3680),v=a.n(u),h=t([_]);function x(){let{user:t,loading:s}=(0,o.a)(),[a,e]=(0,r.useState)(!0),[l,c]=(0,r.useState)([]),[u,h]=(0,r.useState)([]),[x,m]=(0,r.useState)(""),[p,g]=(0,r.useState)("all"),[j,A]=(0,r.useState)("all"),[b,N]=(0,r.useState)("name"),S=[...new Set(l.flatMap(t=>t.specializations))],y=t=>{switch(t){case"active":return v().statusActive;case"inactive":return v().statusInactive;default:return v().statusDefault}},f=t=>{switch(t){case"available":return v().availabilityAvailable;case"busy":return v().availabilityBusy;case"unavailable":return v().availabilityUnavailable;default:return v().availabilityDefault}},C=async t=>{try{c(s=>s.map(s=>s.id===t?{...s,status:"active"===s.status?"inactive":"active"}:s))}catch(t){console.error("Error updating artist status:",t)}},P={totalArtists:l.length,activeArtists:l.filter(t=>"active"===t.status).length,availableArtists:l.filter(t=>"available"===t.availability).length,totalRevenue:l.reduce((t,s)=>t+s.total_revenue,0),avgRating:l.reduce((t,s)=>t+s.rating,0)/l.length};return s||a?i.jsx(_.Z,{children:(0,i.jsxs)("div",{className:v().loadingContainer,children:[i.jsx("div",{className:v().loadingSpinner}),i.jsx("p",{children:"Loading artists..."})]})}):t?(0,i.jsxs)(_.Z,{children:[(0,i.jsxs)(n(),{children:[i.jsx("title",{children:"Artists Management | Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"description",content:"Manage artist profiles and assignments"})]}),(0,i.jsxs)("div",{className:v().artistsContainer,children:[(0,i.jsxs)("header",{className:v().header,children:[i.jsx("h1",{className:v().title,children:"Artists Management"}),i.jsx("div",{className:v().headerActions,children:i.jsx(d(),{href:"/admin/artists/new",className:v().newArtistBtn,children:"+ Add Artist"})})]}),(0,i.jsxs)("div",{className:v().controlsPanel,children:[i.jsx("div",{className:v().searchSection,children:i.jsx("input",{type:"text",placeholder:"Search artists by name, email, or specialization...",value:x,onChange:t=>m(t.target.value),className:v().searchInput})}),(0,i.jsxs)("div",{className:v().filters,children:[(0,i.jsxs)("select",{value:p,onChange:t=>g(t.target.value),className:v().specializationFilter,children:[i.jsx("option",{value:"all",children:"All Specializations"}),S.map(t=>i.jsx("option",{value:t,children:t},t))]}),(0,i.jsxs)("select",{value:j,onChange:t=>A(t.target.value),className:v().statusFilter,children:[i.jsx("option",{value:"all",children:"All Status"}),i.jsx("option",{value:"active",children:"Active"}),i.jsx("option",{value:"inactive",children:"Inactive"})]}),(0,i.jsxs)("select",{value:b,onChange:t=>N(t.target.value),className:v().sortSelect,children:[i.jsx("option",{value:"name",children:"Sort by Name"}),i.jsx("option",{value:"rating",children:"Sort by Rating"}),i.jsx("option",{value:"bookings",children:"Sort by Bookings"}),i.jsx("option",{value:"revenue",children:"Sort by Revenue"})]})]})]}),(0,i.jsxs)("div",{className:v().artistsContent,children:[(0,i.jsxs)("div",{className:v().statsCards,children:[(0,i.jsxs)("div",{className:v().statCard,children:[i.jsx("h3",{children:"Total Artists"}),i.jsx("div",{className:v().statValue,children:P.totalArtists})]}),(0,i.jsxs)("div",{className:v().statCard,children:[i.jsx("h3",{children:"Active Artists"}),i.jsx("div",{className:v().statValue,children:P.activeArtists})]}),(0,i.jsxs)("div",{className:v().statCard,children:[i.jsx("h3",{children:"Available Now"}),i.jsx("div",{className:v().statValue,children:P.availableArtists})]}),(0,i.jsxs)("div",{className:v().statCard,children:[i.jsx("h3",{children:"Total Revenue"}),(0,i.jsxs)("div",{className:v().statValue,children:["$",P.totalRevenue.toLocaleString()]})]}),(0,i.jsxs)("div",{className:v().statCard,children:[i.jsx("h3",{children:"Avg Rating"}),(0,i.jsxs)("div",{className:v().statValue,children:[P.avgRating.toFixed(1),"★"]})]})]}),0===u.length?i.jsx("div",{className:v().emptyState,children:i.jsx("p",{children:"No artists found matching your criteria."})}):i.jsx("div",{className:v().artistsGrid,children:u.map(t=>(0,i.jsxs)("div",{className:v().artistCard,children:[(0,i.jsxs)("div",{className:v().artistHeader,children:[(0,i.jsxs)("div",{className:v().artistInfo,children:[i.jsx("h3",{children:t.name}),i.jsx("p",{children:t.email}),i.jsx("p",{children:t.phone})]}),(0,i.jsxs)("div",{className:v().badges,children:[i.jsx("div",{className:`${v().statusBadge} ${y(t.status)}`,children:t.status}),i.jsx("div",{className:`${v().availabilityBadge} ${f(t.availability)}`,children:t.availability})]})]}),(0,i.jsxs)("div",{className:v().specializations,children:[i.jsx("h4",{children:"Specializations:"}),i.jsx("div",{className:v().specializationTags,children:t.specializations.map(t=>i.jsx("span",{className:v().specializationTag,children:t},t))})]}),(0,i.jsxs)("div",{className:v().artistStats,children:[(0,i.jsxs)("div",{className:v().statItem,children:[i.jsx("span",{className:v().statLabel,children:"Rating:"}),(0,i.jsxs)("span",{className:v().statValue,children:[t.rating,"★"]})]}),(0,i.jsxs)("div",{className:v().statItem,children:[i.jsx("span",{className:v().statLabel,children:"Bookings:"}),i.jsx("span",{className:v().statValue,children:t.total_bookings})]}),(0,i.jsxs)("div",{className:v().statItem,children:[i.jsx("span",{className:v().statLabel,children:"Revenue:"}),(0,i.jsxs)("span",{className:v().statValue,children:["$",t.total_revenue.toLocaleString()]})]}),(0,i.jsxs)("div",{className:v().statItem,children:[i.jsx("span",{className:v().statLabel,children:"Joined:"}),i.jsx("span",{className:v().statValue,children:new Date(t.joined_date).toLocaleDateString()})]})]}),(0,i.jsxs)("div",{className:v().artistActions,children:[i.jsx(d(),{href:`/admin/artists/${t.id}`,className:v().viewBtn,children:"View Profile"}),i.jsx("button",{onClick:()=>C(t.id),className:`${v().toggleBtn} ${"active"===t.status?v().deactivate:v().activate}`,children:"active"===t.status?"Deactivate":"Activate"})]})]},t.id))})]})]})]}):null}_=(h.then?(await h)():h)[0],e()}catch(t){e(t)}})},2785:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:t=>{"use strict";t.exports=require("next/head")},6689:t=>{"use strict";t.exports=require("react")},6405:t=>{"use strict";t.exports=require("react-dom")},997:t=>{"use strict";t.exports=require("react/jsx-runtime")},2048:t=>{"use strict";t.exports=require("fs")},5315:t=>{"use strict";t.exports=require("path")},6162:t=>{"use strict";t.exports=require("stream")},1568:t=>{"use strict";t.exports=require("zlib")},3590:t=>{"use strict";t.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(t);var a=t=>s(s.s=t),e=s.X(0,[899,212,664,441],()=>a(1314));module.exports=e})();