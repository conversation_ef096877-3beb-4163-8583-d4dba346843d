(()=>{var e={};e.id=209,e.ids=[209,660],e.modules={7139:e=>{e.exports={productsContainer:"Products_productsContainer__A_eJy",header:"Products_header__MesnD",title:"Products_title__JVkKQ",headerActions:"Products_headerActions__GgUlt",newProductBtn:"Products_newProductBtn__B6HYU",filtersSection:"Products_filtersSection__PVoFG",searchBar:"Products_searchBar__TQ_b8",searchInput:"Products_searchInput__IpuX4",filters:"Products_filters__4UXYp",filterSelect:"Products_filterSelect__Yj55b",sortSelect:"Products_sortSelect__bw2db",statsBar:"Products_statsBar__WO_hI",stat:"Products_stat__Ervdg",statNumber:"Products_statNumber__YW9mB",statLabel:"Products_statLabel__1hE37",productsGrid:"Products_productsGrid__MUvv7",productCard:"Products_productCard__NnRkS",productImage:"Products_productImage__gvfA_",placeholderImage:"Products_placeholderImage__Oqni0",productInfo:"Products_productInfo__8ul2C",productHeader:"Products_productHeader__PdZEO",productName:"Products_productName__PiTku",statusBadge:"Products_statusBadge__rF99R",active:"Products_active__Ksddh",inactive:"Products_inactive__P5xy_",productDetails:"Products_productDetails__IsBKI",productMeta:"Products_productMeta__WJ2VG",sku:"Products_sku__UiXDl",category:"Products_category__Z3_8B",priceStock:"Products_priceStock__cb6a5",pricing:"Products_pricing__il8UX",price:"Products_price__Vkpvv",salePrice:"Products_salePrice__F6lrv",stock:"Products_stock__n5hZ0",stockStatus:"Products_stockStatus__Xv9LX",inStock:"Products_inStock__S_LsJ",lowStock:"Products_lowStock__TNx9r",outOfStock:"Products_outOfStock__sdTDi",stockCount:"Products_stockCount__mIggZ",description:"Products_description__s2ui2",productActions:"Products_productActions__mAlgI",viewBtn:"Products_viewBtn__hU_0X",editBtn:"Products_editBtn__nrfcL",emptyState:"Products_emptyState__VF4ic",addFirstBtn:"Products_addFirstBtn__vzLL1",loadingContainer:"Products_loadingContainer__ExoGg",errorContainer:"Products_errorContainer__9B_vB",loadingSpinner:"Products_loadingSpinner__M9t_l",spin:"Products_spin__plmlc",retryButton:"Products_retryButton___SoyA"}},9206:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>_,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>k,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>v});var a=s(7093),c=s(5244),i=s(1323),o=s(2899),d=s.n(o),n=s(6814),l=s(5924),u=e([n,l]);[n,l]=u.then?(await u)():u;let _=(0,i.l)(l,"default"),p=(0,i.l)(l,"getStaticProps"),h=(0,i.l)(l,"getStaticPaths"),m=(0,i.l)(l,"getServerSideProps"),x=(0,i.l)(l,"config"),g=(0,i.l)(l,"reportWebVitals"),v=(0,i.l)(l,"unstable_getStaticProps"),P=(0,i.l)(l,"unstable_getStaticPaths"),j=(0,i.l)(l,"unstable_getStaticParams"),S=(0,i.l)(l,"unstable_getServerProps"),N=(0,i.l)(l,"unstable_getServerSideProps"),k=new a.PagesRouteModule({definition:{kind:c.x.PAGES,page:"/admin/products",pathname:"/admin/products",bundlePath:"",filename:""},components:{App:n.default,Document:d()},userland:l});r()}catch(e){r(e)}})},5924:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>m});var a=s(997),c=s(6689),i=s(968),o=s.n(i),d=s(1664),n=s.n(d),l=s(8568),u=s(4845),_=s(7139),p=s.n(_),h=e([u]);function m(){let{user:e,loading:t}=(0,l.a)(),[s,r]=(0,c.useState)(!0),[i,d]=(0,c.useState)([]),[_,h]=(0,c.useState)([]),[m,x]=(0,c.useState)(""),[g,v]=(0,c.useState)(""),[P,j]=(0,c.useState)(""),[S,N]=(0,c.useState)("name"),[k,b]=(0,c.useState)(null),f=async()=>{try{r(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/products",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to load products");let s=await t.json();d(s.products||[])}catch(e){console.error("Error loading products:",e),b(e.message)}finally{r(!1)}},y=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e||0),B=e=>e.stock<=0?{status:"out_of_stock",label:"Out of Stock",class:"outOfStock"}:e.stock<=(e.low_stock_threshold||5)?{status:"low_stock",label:"Low Stock",class:"lowStock"}:{status:"in_stock",label:"In Stock",class:"inStock"},w=[...new Set(i.map(e=>e.category_name).filter(Boolean))];return t||s?a.jsx(u.Z,{children:(0,a.jsxs)("div",{className:p().loadingContainer,children:[a.jsx("div",{className:p().loadingSpinner}),a.jsx("p",{children:"Loading products..."})]})}):k?a.jsx(u.Z,{children:(0,a.jsxs)("div",{className:p().errorContainer,children:[a.jsx("h2",{children:"Error Loading Products"}),a.jsx("p",{children:k}),a.jsx("button",{onClick:f,className:p().retryButton,children:"Try Again"})]})}):(0,a.jsxs)(u.Z,{children:[(0,a.jsxs)(o(),{children:[a.jsx("title",{children:"Products Management | Ocean Soul Sparkles Admin"}),a.jsx("meta",{name:"description",content:"Manage product catalog and inventory"})]}),(0,a.jsxs)("div",{className:p().productsContainer,children:[(0,a.jsxs)("header",{className:p().header,children:[a.jsx("h1",{className:p().title,children:"Products Management"}),a.jsx("div",{className:p().headerActions,children:a.jsx(n(),{href:"/admin/products/new",className:p().newProductBtn,children:"+ Add Product"})})]}),(0,a.jsxs)("div",{className:p().filtersSection,children:[a.jsx("div",{className:p().searchBar,children:a.jsx("input",{type:"text",placeholder:"Search products by name, SKU, or description...",value:m,onChange:e=>x(e.target.value),className:p().searchInput})}),(0,a.jsxs)("div",{className:p().filters,children:[(0,a.jsxs)("select",{value:g,onChange:e=>v(e.target.value),className:p().filterSelect,children:[a.jsx("option",{value:"",children:"All Categories"}),w.map(e=>a.jsx("option",{value:e,children:e},e))]}),(0,a.jsxs)("select",{value:P,onChange:e=>j(e.target.value),className:p().filterSelect,children:[a.jsx("option",{value:"",children:"All Status"}),a.jsx("option",{value:"active",children:"Active"}),a.jsx("option",{value:"inactive",children:"Inactive"}),a.jsx("option",{value:"low_stock",children:"Low Stock"})]}),(0,a.jsxs)("select",{value:S,onChange:e=>N(e.target.value),className:p().sortSelect,children:[a.jsx("option",{value:"name",children:"Sort by Name"}),a.jsx("option",{value:"price",children:"Sort by Price"}),a.jsx("option",{value:"stock",children:"Sort by Stock"}),a.jsx("option",{value:"category",children:"Sort by Category"})]})]})]}),(0,a.jsxs)("div",{className:p().statsBar,children:[(0,a.jsxs)("div",{className:p().stat,children:[a.jsx("span",{className:p().statNumber,children:i.length}),a.jsx("span",{className:p().statLabel,children:"Total Products"})]}),(0,a.jsxs)("div",{className:p().stat,children:[a.jsx("span",{className:p().statNumber,children:i.filter(e=>e.is_active).length}),a.jsx("span",{className:p().statLabel,children:"Active"})]}),(0,a.jsxs)("div",{className:p().stat,children:[a.jsx("span",{className:p().statNumber,children:i.filter(e=>e.stock<=(e.low_stock_threshold||5)).length}),a.jsx("span",{className:p().statLabel,children:"Low Stock"})]}),(0,a.jsxs)("div",{className:p().stat,children:[a.jsx("span",{className:p().statNumber,children:y(i.reduce((e,t)=>e+(t.price||0)*(t.stock||0),0))}),a.jsx("span",{className:p().statLabel,children:"Total Value"})]})]}),a.jsx("div",{className:p().productsGrid,children:0===_.length?(0,a.jsxs)("div",{className:p().emptyState,children:[a.jsx("h3",{children:"No products found"}),a.jsx("p",{children:m||g||P?"Try adjusting your filters or search terms.":"Start by adding your first product."}),!m&&!g&&!P&&a.jsx(n(),{href:"/admin/products/new",className:p().addFirstBtn,children:"Add Your First Product"})]}):_.map(e=>{let t=B(e);return(0,a.jsxs)("div",{className:p().productCard,children:[a.jsx("div",{className:p().productImage,children:e.image_url?a.jsx("img",{src:e.image_url,alt:e.name}):a.jsx("div",{className:p().placeholderImage,children:a.jsx("span",{children:"\uD83D\uDCE6"})})}),(0,a.jsxs)("div",{className:p().productInfo,children:[(0,a.jsxs)("div",{className:p().productHeader,children:[a.jsx("h3",{className:p().productName,children:e.name}),a.jsx("span",{className:`${p().statusBadge} ${p()[e.is_active?"active":"inactive"]}`,children:e.is_active?"Active":"Inactive"})]}),(0,a.jsxs)("div",{className:p().productDetails,children:[(0,a.jsxs)("div",{className:p().productMeta,children:[(0,a.jsxs)("span",{className:p().sku,children:["SKU: ",e.sku||"N/A"]}),a.jsx("span",{className:p().category,children:e.category_name||"Uncategorized"})]}),(0,a.jsxs)("div",{className:p().priceStock,children:[(0,a.jsxs)("div",{className:p().pricing,children:[a.jsx("span",{className:p().price,children:y(e.price)}),e.sale_price&&e.sale_price<e.price&&a.jsx("span",{className:p().salePrice,children:y(e.sale_price)})]}),(0,a.jsxs)("div",{className:p().stock,children:[a.jsx("span",{className:`${p().stockStatus} ${p()[t.class]}`,children:t.label}),(0,a.jsxs)("span",{className:p().stockCount,children:[e.stock||0," units"]})]})]}),e.description&&a.jsx("p",{className:p().description,children:e.description.length>100?`${e.description.substring(0,100)}...`:e.description})]}),(0,a.jsxs)("div",{className:p().productActions,children:[a.jsx(n(),{href:`/admin/products/${e.id}`,className:p().viewBtn,children:"View Details"}),a.jsx(n(),{href:`/admin/products/${e.id}/edit`,className:p().editBtn,children:"Edit"})]})]})]},e.id)})})]})]})}u=(h.then?(await h)():h)[0],r()}catch(e){r(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[899,212,664,441],()=>s(9206));module.exports=r})();