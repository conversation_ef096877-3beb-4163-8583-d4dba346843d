"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/login";
exports.ids = ["pages/api/auth/login"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "speakeasy":
/*!****************************!*\
  !*** external "speakeasy" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("speakeasy");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\auth\\login.ts */ \"(api)/./pages/api/auth/login.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/login\",\n        pathname: \"/api/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/auth/admin-auth.ts":
/*!********************************!*\
  !*** ./lib/auth/admin-auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminLogin: () => (/* binding */ adminLogin),\n/* harmony export */   adminLogout: () => (/* binding */ adminLogout),\n/* harmony export */   enableMFA: () => (/* binding */ enableMFA),\n/* harmony export */   generateMFASecret: () => (/* binding */ generateMFASecret),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyMFAAndLogin: () => (/* binding */ verifyMFAAndLogin)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _security_audit_logging__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../security/audit-logging */ \"(api)/./lib/security/audit-logging.ts\");\n\n\n\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Verify admin authentication token\r\n */ async function verifyAdminToken(token) {\n    try {\n        // Handle missing JWT secret gracefully\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n        // Get user from database with latest info\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        last_activity,\r\n        permissions\r\n      `).eq(\"id\", decoded.userId).eq(\"is_active\", true).single();\n        if (error || !user) {\n            return {\n                valid: false,\n                error: \"User not found or inactive\"\n            };\n        }\n        // Check if user is still active\n        if (!user.is_active) {\n            return {\n                valid: false,\n                error: \"User account is deactivated\"\n            };\n        }\n        // Update last activity\n        await supabase.from(\"admin_users\").update({\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        return {\n            valid: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            error: \"Invalid token\"\n        };\n    }\n}\n/**\r\n * Admin login with email and password\r\n */ async function adminLogin(email, password, ip) {\n    try {\n        // Check for rate limiting\n        const { data: attempts } = await supabase.from(\"login_attempts\").select(\"*\").eq(\"email\", email).gte(\"created_at\", new Date(Date.now() - 15 * 60 * 1000).toISOString()).order(\"created_at\", {\n            ascending: false\n        });\n        if (attempts && attempts.length >= 5) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_BLOCKED\",\n                email,\n                ip,\n                reason: \"Too many failed attempts\"\n            });\n            return {\n                success: false,\n                error: \"Account temporarily locked due to too many failed attempts\"\n            };\n        }\n        // Get user from database\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        password_hash,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"email\", email.toLowerCase()).single();\n        if (error || !user) {\n            await recordFailedAttempt(email, ip, error ? `Database error: ${error.message}` : \"User not found\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Check if user is active\n        if (!user.is_active) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_DENIED\",\n                userId: user.id,\n                email,\n                ip,\n                reason: \"Account deactivated\"\n            });\n            return {\n                success: false,\n                error: \"Account is deactivated\"\n            };\n        }\n        // Verify password\n        const passwordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n        if (!passwordValid) {\n            await recordFailedAttempt(email, ip, \"Invalid password\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Clear failed attempts on successful password verification\n        await supabase.from(\"login_attempts\").delete().eq(\"email\", email);\n        // Check if MFA is required\n        if (user.mfa_enabled && user.mfa_secret) {\n            // Return success but indicate MFA is required\n            return {\n                success: true,\n                requiresMFA: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    firstName: user.first_name,\n                    lastName: user.last_name,\n                    isActive: user.is_active,\n                    mfaEnabled: user.mfa_enabled,\n                    lastActivity: Date.now(),\n                    permissions: user.permissions || []\n                }\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"Admin login error:\", error);\n        return {\n            success: false,\n            error: \"Login failed\"\n        };\n    }\n}\n/**\r\n * Verify MFA token and complete login\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function verifyMFAAndLogin(userId, mfaToken, ip) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"id\", userId).single();\n        if (error || !user || !user.mfa_secret) {\n            return {\n                success: false,\n                error: \"Invalid MFA setup\"\n            };\n        }\n        // Verify MFA token\n        const verified = speakeasy.totp.verify({\n            secret: user.mfa_secret,\n            encoding: \"base32\",\n            token: mfaToken,\n            window: 2\n        });\n        if (!verified) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"MFA_FAILED\",\n                userId: user.id,\n                email: user.email,\n                ip,\n                reason: \"Invalid MFA token\"\n            });\n            return {\n                success: false,\n                error: \"Invalid MFA token\"\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email: user.email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"MFA verification error:\", error);\n        return {\n            success: false,\n            error: \"MFA verification failed\"\n        };\n    }\n}\n/**\r\n * Generate MFA secret for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function generateMFASecret(userId) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user } = await supabase.from(\"admin_users\").select(\"email, first_name, last_name\").eq(\"id\", userId).single();\n        if (!user) return null;\n        const secret = speakeasy.generateSecret({\n            name: `${user.first_name} ${user.last_name}`,\n            issuer: \"Ocean Soul Sparkles Admin\",\n            length: 32\n        });\n        return {\n            secret: secret.base32,\n            qrCode: secret.otpauth_url\n        };\n    } catch (error) {\n        console.error(\"MFA secret generation error:\", error);\n        return null;\n    }\n}\n/**\r\n * Enable MFA for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function enableMFA(userId, secret, token) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        // Verify the token first\n        const verified = speakeasy.totp.verify({\n            secret,\n            encoding: \"base32\",\n            token,\n            window: 2\n        });\n        if (!verified) return false;\n        // Save MFA secret to database\n        const { error } = await supabase.from(\"admin_users\").update({\n            mfa_secret: secret,\n            mfa_enabled: true\n        }).eq(\"id\", userId);\n        if (error) return false;\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_ENABLED\",\n            userId,\n            reason: \"User enabled MFA\"\n        });\n        return true;\n    } catch (error) {\n        console.error(\"MFA enable error:\", error);\n        return false;\n    }\n}\n/**\r\n * Record failed login attempt\r\n */ async function recordFailedAttempt(email, ip, reason) {\n    await supabase.from(\"login_attempts\").insert({\n        email,\n        ip_address: ip,\n        success: false,\n        reason,\n        created_at: new Date().toISOString()\n    });\n    await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n        action: \"LOGIN_FAILED\",\n        email,\n        ip,\n        reason\n    });\n}\n/**\r\n * Admin logout\r\n */ async function adminLogout(userId, ip) {\n    try {\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGOUT\",\n            userId,\n            ip\n        });\n    } catch (error) {\n        console.error(\"Logout audit error:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvYXV0aC9hZG1pbi1hdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDRDtBQUN1QjtBQUNBO0FBRXJELGtEQUFrRDtBQUNsRCxNQUFNSSxjQUFjQywwQ0FBb0MsSUFBSTtBQUM1RCxNQUFNRyxjQUFjSCxRQUFRQyxHQUFHLENBQUNHLHlCQUF5QixJQUFJO0FBRTdELE1BQU1DLFdBQVdSLG1FQUFZQSxDQUFDRSxhQUFhSTtBQTZCM0M7O0NBRUMsR0FDTSxlQUFlRyxpQkFBaUJDLEtBQWE7SUFDbEQsSUFBSTtRQUNGLHVDQUF1QztRQUN2QyxNQUFNQyxZQUFZUixRQUFRQyxHQUFHLENBQUNRLFVBQVUsSUFBSTtRQUM1QyxNQUFNQyxVQUFVZiwwREFBVSxDQUFDWSxPQUFPQztRQUVsQywwQ0FBMEM7UUFDMUMsTUFBTSxFQUFFSSxNQUFNQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1ULFNBQ2pDVSxJQUFJLENBQUMsZUFDTEMsTUFBTSxDQUFDLENBQUM7Ozs7Ozs7Ozs7TUFVVCxDQUFDLEVBQ0FDLEVBQUUsQ0FBQyxNQUFNUCxRQUFRUSxNQUFNLEVBQ3ZCRCxFQUFFLENBQUMsYUFBYSxNQUNoQkUsTUFBTTtRQUVULElBQUlMLFNBQVMsQ0FBQ0QsTUFBTTtZQUNsQixPQUFPO2dCQUFFTyxPQUFPO2dCQUFPTixPQUFPO1lBQTZCO1FBQzdEO1FBRUEsZ0NBQWdDO1FBQ2hDLElBQUksQ0FBQ0QsS0FBS1EsU0FBUyxFQUFFO1lBQ25CLE9BQU87Z0JBQUVELE9BQU87Z0JBQU9OLE9BQU87WUFBOEI7UUFDOUQ7UUFFQSx1QkFBdUI7UUFDdkIsTUFBTVQsU0FDSFUsSUFBSSxDQUFDLGVBQ0xPLE1BQU0sQ0FBQztZQUFFQyxlQUFlQyxLQUFLQyxHQUFHO1FBQUcsR0FDbkNSLEVBQUUsQ0FBQyxNQUFNSixLQUFLYSxFQUFFO1FBRW5CLE9BQU87WUFDTE4sT0FBTztZQUNQUCxNQUFNO2dCQUNKYSxJQUFJYixLQUFLYSxFQUFFO2dCQUNYQyxPQUFPZCxLQUFLYyxLQUFLO2dCQUNqQkMsTUFBTWYsS0FBS2UsSUFBSTtnQkFDZkMsV0FBV2hCLEtBQUtpQixVQUFVO2dCQUMxQkMsVUFBVWxCLEtBQUttQixTQUFTO2dCQUN4QkMsVUFBVXBCLEtBQUtRLFNBQVM7Z0JBQ3hCYSxZQUFZckIsS0FBS3NCLFdBQVc7Z0JBQzVCQyxjQUFjWixLQUFLQyxHQUFHO2dCQUN0QlksYUFBYXhCLEtBQUt3QixXQUFXLElBQUksRUFBRTtZQUNyQztRQUNGO0lBQ0YsRUFBRSxPQUFPdkIsT0FBTztRQUNkLE9BQU87WUFBRU0sT0FBTztZQUFPTixPQUFPO1FBQWdCO0lBQ2hEO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWV3QixXQUNwQlgsS0FBYSxFQUNiWSxRQUFnQixFQUNoQkMsRUFBVztJQUVYLElBQUk7UUFDRiwwQkFBMEI7UUFDMUIsTUFBTSxFQUFFNUIsTUFBTTZCLFFBQVEsRUFBRSxHQUFHLE1BQU1wQyxTQUM5QlUsSUFBSSxDQUFDLGtCQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFNBQVNVLE9BQ1plLEdBQUcsQ0FBQyxjQUFjLElBQUlsQixLQUFLQSxLQUFLQyxHQUFHLEtBQUssS0FBSyxLQUFLLE1BQU1rQixXQUFXLElBQ25FQyxLQUFLLENBQUMsY0FBYztZQUFFQyxXQUFXO1FBQU07UUFFMUMsSUFBSUosWUFBWUEsU0FBU0ssTUFBTSxJQUFJLEdBQUc7WUFDcEMsTUFBTWhELGlFQUFRQSxDQUFDO2dCQUNiaUQsUUFBUTtnQkFDUnBCO2dCQUNBYTtnQkFDQVEsUUFBUTtZQUNWO1lBQ0EsT0FBTztnQkFBRUMsU0FBUztnQkFBT25DLE9BQU87WUFBNkQ7UUFDL0Y7UUFFQSx5QkFBeUI7UUFDekIsTUFBTSxFQUFFRixNQUFNQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1ULFNBQ2pDVSxJQUFJLENBQUMsZUFDTEMsTUFBTSxDQUFDLENBQUM7Ozs7Ozs7Ozs7O01BV1QsQ0FBQyxFQUNBQyxFQUFFLENBQUMsU0FBU1UsTUFBTXVCLFdBQVcsSUFDN0IvQixNQUFNO1FBSVQsSUFBSUwsU0FBUyxDQUFDRCxNQUFNO1lBQ2xCLE1BQU1zQyxvQkFBb0J4QixPQUFPYSxJQUFJMUIsUUFBUSxDQUFDLGdCQUFnQixFQUFFQSxNQUFNc0MsT0FBTyxDQUFDLENBQUMsR0FBRztZQUNsRixPQUFPO2dCQUFFSCxTQUFTO2dCQUFPbkMsT0FBTztZQUFzQjtRQUN4RDtRQUVBLDBCQUEwQjtRQUMxQixJQUFJLENBQUNELEtBQUtRLFNBQVMsRUFBRTtZQUNuQixNQUFNdkIsaUVBQVFBLENBQUM7Z0JBQ2JpRCxRQUFRO2dCQUNSN0IsUUFBUUwsS0FBS2EsRUFBRTtnQkFDZkM7Z0JBQ0FhO2dCQUNBUSxRQUFRO1lBQ1Y7WUFDQSxPQUFPO2dCQUFFQyxTQUFTO2dCQUFPbkMsT0FBTztZQUF5QjtRQUMzRDtRQUVBLGtCQUFrQjtRQUNsQixNQUFNdUMsZ0JBQWdCLE1BQU16RCx1REFBYyxDQUFDMkMsVUFBVTFCLEtBQUswQyxhQUFhO1FBQ3ZFLElBQUksQ0FBQ0YsZUFBZTtZQUNsQixNQUFNRixvQkFBb0J4QixPQUFPYSxJQUFJO1lBQ3JDLE9BQU87Z0JBQUVTLFNBQVM7Z0JBQU9uQyxPQUFPO1lBQXNCO1FBQ3hEO1FBRUEsNERBQTREO1FBQzVELE1BQU1ULFNBQ0hVLElBQUksQ0FBQyxrQkFDTHlDLE1BQU0sR0FDTnZDLEVBQUUsQ0FBQyxTQUFTVTtRQUVmLDJCQUEyQjtRQUMzQixJQUFJZCxLQUFLc0IsV0FBVyxJQUFJdEIsS0FBSzRDLFVBQVUsRUFBRTtZQUN2Qyw4Q0FBOEM7WUFDOUMsT0FBTztnQkFDTFIsU0FBUztnQkFDVFMsYUFBYTtnQkFDYjdDLE1BQU07b0JBQ0phLElBQUliLEtBQUthLEVBQUU7b0JBQ1hDLE9BQU9kLEtBQUtjLEtBQUs7b0JBQ2pCQyxNQUFNZixLQUFLZSxJQUFJO29CQUNmQyxXQUFXaEIsS0FBS2lCLFVBQVU7b0JBQzFCQyxVQUFVbEIsS0FBS21CLFNBQVM7b0JBQ3hCQyxVQUFVcEIsS0FBS1EsU0FBUztvQkFDeEJhLFlBQVlyQixLQUFLc0IsV0FBVztvQkFDNUJDLGNBQWNaLEtBQUtDLEdBQUc7b0JBQ3RCWSxhQUFheEIsS0FBS3dCLFdBQVcsSUFBSSxFQUFFO2dCQUNyQztZQUNGO1FBQ0Y7UUFFQSxxQkFBcUI7UUFDckIsTUFBTTdCLFlBQVlSLFFBQVFDLEdBQUcsQ0FBQ1EsVUFBVSxJQUFJO1FBQzVDLE1BQU1GLFFBQVFaLHdEQUFRLENBQ3BCO1lBQ0V1QixRQUFRTCxLQUFLYSxFQUFFO1lBQ2ZDLE9BQU9kLEtBQUtjLEtBQUs7WUFDakJDLE1BQU1mLEtBQUtlLElBQUk7UUFDakIsR0FDQXBCLFdBQ0E7WUFBRW9ELFdBQVc7UUFBSztRQUdwQixvQkFBb0I7UUFDcEIsTUFBTXZELFNBQ0hVLElBQUksQ0FBQyxlQUNMTyxNQUFNLENBQUM7WUFDTnVDLGVBQWUsSUFBSXJDLE9BQU9tQixXQUFXO1lBQ3JDcEIsZUFBZUMsS0FBS0MsR0FBRztRQUN6QixHQUNDUixFQUFFLENBQUMsTUFBTUosS0FBS2EsRUFBRTtRQUVuQixNQUFNNUIsaUVBQVFBLENBQUM7WUFDYmlELFFBQVE7WUFDUjdCLFFBQVFMLEtBQUthLEVBQUU7WUFDZm9DLFVBQVVqRCxLQUFLZSxJQUFJO1lBQ25CRDtZQUNBYTtRQUNGO1FBRUEsT0FBTztZQUNMUyxTQUFTO1lBQ1QxQztZQUNBTSxNQUFNO2dCQUNKYSxJQUFJYixLQUFLYSxFQUFFO2dCQUNYQyxPQUFPZCxLQUFLYyxLQUFLO2dCQUNqQkMsTUFBTWYsS0FBS2UsSUFBSTtnQkFDZkMsV0FBV2hCLEtBQUtpQixVQUFVO2dCQUMxQkMsVUFBVWxCLEtBQUttQixTQUFTO2dCQUN4QkMsVUFBVXBCLEtBQUtRLFNBQVM7Z0JBQ3hCYSxZQUFZckIsS0FBS3NCLFdBQVc7Z0JBQzVCQyxjQUFjWixLQUFLQyxHQUFHO2dCQUN0QlksYUFBYXhCLEtBQUt3QixXQUFXLElBQUksRUFBRTtZQUNyQztRQUNGO0lBQ0YsRUFBRSxPQUFPdkIsT0FBTztRQUNkaUQsUUFBUWpELEtBQUssQ0FBQyxzQkFBc0JBO1FBQ3BDLE9BQU87WUFBRW1DLFNBQVM7WUFBT25DLE9BQU87UUFBZTtJQUNqRDtBQUNGO0FBRUE7OztDQUdDLEdBQ00sZUFBZWtELGtCQUNwQjlDLE1BQWMsRUFDZCtDLFFBQWdCLEVBQ2hCekIsRUFBVztJQUVYLDREQUE0RDtJQUM1RCxNQUFNMEIsWUFBWSxNQUFNLHdIQUFPO0lBRS9CLElBQUk7UUFDRixNQUFNLEVBQUV0RCxNQUFNQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1ULFNBQ2pDVSxJQUFJLENBQUMsZUFDTEMsTUFBTSxDQUFDLENBQUM7Ozs7Ozs7Ozs7TUFVVCxDQUFDLEVBQ0FDLEVBQUUsQ0FBQyxNQUFNQyxRQUNUQyxNQUFNO1FBRVQsSUFBSUwsU0FBUyxDQUFDRCxRQUFRLENBQUNBLEtBQUs0QyxVQUFVLEVBQUU7WUFDdEMsT0FBTztnQkFBRVIsU0FBUztnQkFBT25DLE9BQU87WUFBb0I7UUFDdEQ7UUFFQSxtQkFBbUI7UUFDbkIsTUFBTXFELFdBQVdELFVBQVVFLElBQUksQ0FBQ3pELE1BQU0sQ0FBQztZQUNyQzBELFFBQVF4RCxLQUFLNEMsVUFBVTtZQUN2QmEsVUFBVTtZQUNWL0QsT0FBTzBEO1lBQ1BNLFFBQVE7UUFDVjtRQUVBLElBQUksQ0FBQ0osVUFBVTtZQUNiLE1BQU1yRSxpRUFBUUEsQ0FBQztnQkFDYmlELFFBQVE7Z0JBQ1I3QixRQUFRTCxLQUFLYSxFQUFFO2dCQUNmQyxPQUFPZCxLQUFLYyxLQUFLO2dCQUNqQmE7Z0JBQ0FRLFFBQVE7WUFDVjtZQUNBLE9BQU87Z0JBQUVDLFNBQVM7Z0JBQU9uQyxPQUFPO1lBQW9CO1FBQ3REO1FBRUEscUJBQXFCO1FBQ3JCLE1BQU1OLFlBQVlSLFFBQVFDLEdBQUcsQ0FBQ1EsVUFBVSxJQUFJO1FBQzVDLE1BQU1GLFFBQVFaLHdEQUFRLENBQ3BCO1lBQ0V1QixRQUFRTCxLQUFLYSxFQUFFO1lBQ2ZDLE9BQU9kLEtBQUtjLEtBQUs7WUFDakJDLE1BQU1mLEtBQUtlLElBQUk7UUFDakIsR0FDQXBCLFdBQ0E7WUFBRW9ELFdBQVc7UUFBSztRQUdwQixvQkFBb0I7UUFDcEIsTUFBTXZELFNBQ0hVLElBQUksQ0FBQyxlQUNMTyxNQUFNLENBQUM7WUFDTnVDLGVBQWUsSUFBSXJDLE9BQU9tQixXQUFXO1lBQ3JDcEIsZUFBZUMsS0FBS0MsR0FBRztRQUN6QixHQUNDUixFQUFFLENBQUMsTUFBTUosS0FBS2EsRUFBRTtRQUVuQixNQUFNNUIsaUVBQVFBLENBQUM7WUFDYmlELFFBQVE7WUFDUjdCLFFBQVFMLEtBQUthLEVBQUU7WUFDZm9DLFVBQVVqRCxLQUFLZSxJQUFJO1lBQ25CRCxPQUFPZCxLQUFLYyxLQUFLO1lBQ2pCYTtRQUNGO1FBRUEsT0FBTztZQUNMUyxTQUFTO1lBQ1QxQztZQUNBTSxNQUFNO2dCQUNKYSxJQUFJYixLQUFLYSxFQUFFO2dCQUNYQyxPQUFPZCxLQUFLYyxLQUFLO2dCQUNqQkMsTUFBTWYsS0FBS2UsSUFBSTtnQkFDZkMsV0FBV2hCLEtBQUtpQixVQUFVO2dCQUMxQkMsVUFBVWxCLEtBQUttQixTQUFTO2dCQUN4QkMsVUFBVXBCLEtBQUtRLFNBQVM7Z0JBQ3hCYSxZQUFZckIsS0FBS3NCLFdBQVc7Z0JBQzVCQyxjQUFjWixLQUFLQyxHQUFHO2dCQUN0QlksYUFBYXhCLEtBQUt3QixXQUFXLElBQUksRUFBRTtZQUNyQztRQUNGO0lBQ0YsRUFBRSxPQUFPdkIsT0FBTztRQUNkaUQsUUFBUWpELEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE9BQU87WUFBRW1DLFNBQVM7WUFBT25DLE9BQU87UUFBMEI7SUFDNUQ7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWUwRCxrQkFBa0J0RCxNQUFjO0lBQ3BELDREQUE0RDtJQUM1RCxNQUFNZ0QsWUFBWSxNQUFNLHdIQUFPO0lBRS9CLElBQUk7UUFDRixNQUFNLEVBQUV0RCxNQUFNQyxJQUFJLEVBQUUsR0FBRyxNQUFNUixTQUMxQlUsSUFBSSxDQUFDLGVBQ0xDLE1BQU0sQ0FBQyxnQ0FDUEMsRUFBRSxDQUFDLE1BQU1DLFFBQ1RDLE1BQU07UUFFVCxJQUFJLENBQUNOLE1BQU0sT0FBTztRQUVsQixNQUFNd0QsU0FBU0gsVUFBVU8sY0FBYyxDQUFDO1lBQ3RDQyxNQUFNLENBQUMsRUFBRTdELEtBQUtpQixVQUFVLENBQUMsQ0FBQyxFQUFFakIsS0FBS21CLFNBQVMsQ0FBQyxDQUFDO1lBQzVDMkMsUUFBUTtZQUNSN0IsUUFBUTtRQUNWO1FBRUEsT0FBTztZQUNMdUIsUUFBUUEsT0FBT08sTUFBTTtZQUNyQkMsUUFBUVIsT0FBT1MsV0FBVztRQUM1QjtJQUNGLEVBQUUsT0FBT2hFLE9BQU87UUFDZGlELFFBQVFqRCxLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxPQUFPO0lBQ1Q7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVpRSxVQUFVN0QsTUFBYyxFQUFFbUQsTUFBYyxFQUFFOUQsS0FBYTtJQUMzRSw0REFBNEQ7SUFDNUQsTUFBTTJELFlBQVksTUFBTSx3SEFBTztJQUUvQixJQUFJO1FBQ0YseUJBQXlCO1FBQ3pCLE1BQU1DLFdBQVdELFVBQVVFLElBQUksQ0FBQ3pELE1BQU0sQ0FBQztZQUNyQzBEO1lBQ0FDLFVBQVU7WUFDVi9EO1lBQ0FnRSxRQUFRO1FBQ1Y7UUFFQSxJQUFJLENBQUNKLFVBQVUsT0FBTztRQUV0Qiw4QkFBOEI7UUFDOUIsTUFBTSxFQUFFckQsS0FBSyxFQUFFLEdBQUcsTUFBTVQsU0FDckJVLElBQUksQ0FBQyxlQUNMTyxNQUFNLENBQUM7WUFDTm1DLFlBQVlZO1lBQ1psQyxhQUFhO1FBQ2YsR0FDQ2xCLEVBQUUsQ0FBQyxNQUFNQztRQUVaLElBQUlKLE9BQU8sT0FBTztRQUVsQixNQUFNaEIsaUVBQVFBLENBQUM7WUFDYmlELFFBQVE7WUFDUjdCO1lBQ0E4QixRQUFRO1FBQ1Y7UUFFQSxPQUFPO0lBQ1QsRUFBRSxPQUFPbEMsT0FBTztRQUNkaUQsUUFBUWpELEtBQUssQ0FBQyxxQkFBcUJBO1FBQ25DLE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDRCxlQUFlcUMsb0JBQW9CeEIsS0FBYSxFQUFFYSxFQUFXLEVBQUVRLE1BQWU7SUFDNUUsTUFBTTNDLFNBQ0hVLElBQUksQ0FBQyxrQkFDTGlFLE1BQU0sQ0FBQztRQUNOckQ7UUFDQXNELFlBQVl6QztRQUNaUyxTQUFTO1FBQ1REO1FBQ0FrQyxZQUFZLElBQUkxRCxPQUFPbUIsV0FBVztJQUNwQztJQUVGLE1BQU03QyxpRUFBUUEsQ0FBQztRQUNiaUQsUUFBUTtRQUNScEI7UUFDQWE7UUFDQVE7SUFDRjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFlbUMsWUFBWWpFLE1BQWMsRUFBRXNCLEVBQVc7SUFDM0QsSUFBSTtRQUNGLE1BQU0xQyxpRUFBUUEsQ0FBQztZQUNiaUQsUUFBUTtZQUNSN0I7WUFDQXNCO1FBQ0Y7SUFDRixFQUFFLE9BQU8xQixPQUFPO1FBQ2RpRCxRQUFRakQsS0FBSyxDQUFDLHVCQUF1QkE7SUFDdkM7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL29jZWFuc291bHNwYXJrbGVzLWFkbWluLy4vbGliL2F1dGgvYWRtaW4tYXV0aC50cz9lOTkyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcclxuaW1wb3J0IGJjcnlwdCBmcm9tICdiY3J5cHRqcyc7XHJcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcyc7XHJcbmltcG9ydCB7IGF1ZGl0TG9nIH0gZnJvbSAnLi4vc2VjdXJpdHkvYXVkaXQtbG9nZ2luZyc7XHJcblxyXG4vLyBIYW5kbGUgbWlzc2luZyBlbnZpcm9ubWVudCB2YXJpYWJsZXMgZ3JhY2VmdWxseVxyXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCB8fCAnaHR0cHM6Ly9wbGFjZWhvbGRlci5zdXBhYmFzZS5jbyc7XHJcbmNvbnN0IHN1cGFiYXNlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSB8fCAnZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LnBsYWNlaG9sZGVyJztcclxuXHJcbmNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSk7XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEFkbWluVXNlciB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBlbWFpbDogc3RyaW5nO1xyXG4gIHJvbGU6ICdERVYnIHwgJ0FkbWluJyB8ICdBcnRpc3QnIHwgJ0JyYWlkZXInO1xyXG4gIGZpcnN0TmFtZTogc3RyaW5nO1xyXG4gIGxhc3ROYW1lOiBzdHJpbmc7XHJcbiAgaXNBY3RpdmU6IGJvb2xlYW47XHJcbiAgbWZhRW5hYmxlZDogYm9vbGVhbjtcclxuICBsYXN0QWN0aXZpdHk6IG51bWJlcjtcclxuICBwZXJtaXNzaW9uczogc3RyaW5nW107XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQXV0aFJlc3VsdCB7XHJcbiAgdmFsaWQ6IGJvb2xlYW47XHJcbiAgdXNlcj86IEFkbWluVXNlcjtcclxuICBlcnJvcj86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBMb2dpblJlc3VsdCB7XHJcbiAgc3VjY2VzczogYm9vbGVhbjtcclxuICB0b2tlbj86IHN0cmluZztcclxuICB1c2VyPzogQWRtaW5Vc2VyO1xyXG4gIHJlcXVpcmVzTUZBPzogYm9vbGVhbjtcclxuICBtZmFTZWNyZXQ/OiBzdHJpbmc7XHJcbiAgZXJyb3I/OiBzdHJpbmc7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBWZXJpZnkgYWRtaW4gYXV0aGVudGljYXRpb24gdG9rZW5cclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2ZXJpZnlBZG1pblRva2VuKHRva2VuOiBzdHJpbmcpOiBQcm9taXNlPEF1dGhSZXN1bHQ+IHtcclxuICB0cnkge1xyXG4gICAgLy8gSGFuZGxlIG1pc3NpbmcgSldUIHNlY3JldCBncmFjZWZ1bGx5XHJcbiAgICBjb25zdCBqd3RTZWNyZXQgPSBwcm9jZXNzLmVudi5KV1RfU0VDUkVUIHx8ICdwbGFjZWhvbGRlci1zZWNyZXQnO1xyXG4gICAgY29uc3QgZGVjb2RlZCA9IGp3dC52ZXJpZnkodG9rZW4sIGp3dFNlY3JldCkgYXMgYW55O1xyXG4gICAgXHJcbiAgICAvLyBHZXQgdXNlciBmcm9tIGRhdGFiYXNlIHdpdGggbGF0ZXN0IGluZm9cclxuICAgIGNvbnN0IHsgZGF0YTogdXNlciwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKCdhZG1pbl91c2VycycpXHJcbiAgICAgIC5zZWxlY3QoYFxyXG4gICAgICAgIGlkLFxyXG4gICAgICAgIGVtYWlsLFxyXG4gICAgICAgIHJvbGUsXHJcbiAgICAgICAgZmlyc3RfbmFtZSxcclxuICAgICAgICBsYXN0X25hbWUsXHJcbiAgICAgICAgaXNfYWN0aXZlLFxyXG4gICAgICAgIG1mYV9lbmFibGVkLFxyXG4gICAgICAgIGxhc3RfYWN0aXZpdHksXHJcbiAgICAgICAgcGVybWlzc2lvbnNcclxuICAgICAgYClcclxuICAgICAgLmVxKCdpZCcsIGRlY29kZWQudXNlcklkKVxyXG4gICAgICAuZXEoJ2lzX2FjdGl2ZScsIHRydWUpXHJcbiAgICAgIC5zaW5nbGUoKTtcclxuXHJcbiAgICBpZiAoZXJyb3IgfHwgIXVzZXIpIHtcclxuICAgICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCBlcnJvcjogJ1VzZXIgbm90IGZvdW5kIG9yIGluYWN0aXZlJyB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgc3RpbGwgYWN0aXZlXHJcbiAgICBpZiAoIXVzZXIuaXNfYWN0aXZlKSB7XHJcbiAgICAgIHJldHVybiB7IHZhbGlkOiBmYWxzZSwgZXJyb3I6ICdVc2VyIGFjY291bnQgaXMgZGVhY3RpdmF0ZWQnIH07XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVXBkYXRlIGxhc3QgYWN0aXZpdHlcclxuICAgIGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKCdhZG1pbl91c2VycycpXHJcbiAgICAgIC51cGRhdGUoeyBsYXN0X2FjdGl2aXR5OiBEYXRlLm5vdygpIH0pXHJcbiAgICAgIC5lcSgnaWQnLCB1c2VyLmlkKTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICB2YWxpZDogdHJ1ZSxcclxuICAgICAgdXNlcjoge1xyXG4gICAgICAgIGlkOiB1c2VyLmlkLFxyXG4gICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICAgIHJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgICBmaXJzdE5hbWU6IHVzZXIuZmlyc3RfbmFtZSxcclxuICAgICAgICBsYXN0TmFtZTogdXNlci5sYXN0X25hbWUsXHJcbiAgICAgICAgaXNBY3RpdmU6IHVzZXIuaXNfYWN0aXZlLFxyXG4gICAgICAgIG1mYUVuYWJsZWQ6IHVzZXIubWZhX2VuYWJsZWQsXHJcbiAgICAgICAgbGFzdEFjdGl2aXR5OiBEYXRlLm5vdygpLFxyXG4gICAgICAgIHBlcm1pc3Npb25zOiB1c2VyLnBlcm1pc3Npb25zIHx8IFtdXHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIHJldHVybiB7IHZhbGlkOiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIHRva2VuJyB9O1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEFkbWluIGxvZ2luIHdpdGggZW1haWwgYW5kIHBhc3N3b3JkXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gYWRtaW5Mb2dpbihcclxuICBlbWFpbDogc3RyaW5nLFxyXG4gIHBhc3N3b3JkOiBzdHJpbmcsXHJcbiAgaXA/OiBzdHJpbmdcclxuKTogUHJvbWlzZTxMb2dpblJlc3VsdD4ge1xyXG4gIHRyeSB7XHJcbiAgICAvLyBDaGVjayBmb3IgcmF0ZSBsaW1pdGluZ1xyXG4gICAgY29uc3QgeyBkYXRhOiBhdHRlbXB0cyB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oJ2xvZ2luX2F0dGVtcHRzJylcclxuICAgICAgLnNlbGVjdCgnKicpXHJcbiAgICAgIC5lcSgnZW1haWwnLCBlbWFpbClcclxuICAgICAgLmd0ZSgnY3JlYXRlZF9hdCcsIG5ldyBEYXRlKERhdGUubm93KCkgLSAxNSAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSlcclxuICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pO1xyXG5cclxuICAgIGlmIChhdHRlbXB0cyAmJiBhdHRlbXB0cy5sZW5ndGggPj0gNSkge1xyXG4gICAgICBhd2FpdCBhdWRpdExvZyh7XHJcbiAgICAgICAgYWN0aW9uOiAnTE9HSU5fQkxPQ0tFRCcsXHJcbiAgICAgICAgZW1haWwsXHJcbiAgICAgICAgaXAsXHJcbiAgICAgICAgcmVhc29uOiAnVG9vIG1hbnkgZmFpbGVkIGF0dGVtcHRzJ1xyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQWNjb3VudCB0ZW1wb3JhcmlseSBsb2NrZWQgZHVlIHRvIHRvbyBtYW55IGZhaWxlZCBhdHRlbXB0cycgfTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBHZXQgdXNlciBmcm9tIGRhdGFiYXNlXHJcbiAgICBjb25zdCB7IGRhdGE6IHVzZXIsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbSgnYWRtaW5fdXNlcnMnKVxyXG4gICAgICAuc2VsZWN0KGBcclxuICAgICAgICBpZCxcclxuICAgICAgICBlbWFpbCxcclxuICAgICAgICBwYXNzd29yZF9oYXNoLFxyXG4gICAgICAgIHJvbGUsXHJcbiAgICAgICAgZmlyc3RfbmFtZSxcclxuICAgICAgICBsYXN0X25hbWUsXHJcbiAgICAgICAgaXNfYWN0aXZlLFxyXG4gICAgICAgIG1mYV9lbmFibGVkLFxyXG4gICAgICAgIG1mYV9zZWNyZXQsXHJcbiAgICAgICAgcGVybWlzc2lvbnNcclxuICAgICAgYClcclxuICAgICAgLmVxKCdlbWFpbCcsIGVtYWlsLnRvTG93ZXJDYXNlKCkpXHJcbiAgICAgIC5zaW5nbGUoKTtcclxuXHJcblxyXG5cclxuICAgIGlmIChlcnJvciB8fCAhdXNlcikge1xyXG4gICAgICBhd2FpdCByZWNvcmRGYWlsZWRBdHRlbXB0KGVtYWlsLCBpcCwgZXJyb3IgPyBgRGF0YWJhc2UgZXJyb3I6ICR7ZXJyb3IubWVzc2FnZX1gIDogJ1VzZXIgbm90IGZvdW5kJyk7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ludmFsaWQgY3JlZGVudGlhbHMnIH07XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhY3RpdmVcclxuICAgIGlmICghdXNlci5pc19hY3RpdmUpIHtcclxuICAgICAgYXdhaXQgYXVkaXRMb2coe1xyXG4gICAgICAgIGFjdGlvbjogJ0xPR0lOX0RFTklFRCcsXHJcbiAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxyXG4gICAgICAgIGVtYWlsLFxyXG4gICAgICAgIGlwLFxyXG4gICAgICAgIHJlYXNvbjogJ0FjY291bnQgZGVhY3RpdmF0ZWQnXHJcbiAgICAgIH0pO1xyXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdBY2NvdW50IGlzIGRlYWN0aXZhdGVkJyB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFZlcmlmeSBwYXNzd29yZFxyXG4gICAgY29uc3QgcGFzc3dvcmRWYWxpZCA9IGF3YWl0IGJjcnlwdC5jb21wYXJlKHBhc3N3b3JkLCB1c2VyLnBhc3N3b3JkX2hhc2gpO1xyXG4gICAgaWYgKCFwYXNzd29yZFZhbGlkKSB7XHJcbiAgICAgIGF3YWl0IHJlY29yZEZhaWxlZEF0dGVtcHQoZW1haWwsIGlwLCAnSW52YWxpZCBwYXNzd29yZCcpO1xyXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIGNyZWRlbnRpYWxzJyB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENsZWFyIGZhaWxlZCBhdHRlbXB0cyBvbiBzdWNjZXNzZnVsIHBhc3N3b3JkIHZlcmlmaWNhdGlvblxyXG4gICAgYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oJ2xvZ2luX2F0dGVtcHRzJylcclxuICAgICAgLmRlbGV0ZSgpXHJcbiAgICAgIC5lcSgnZW1haWwnLCBlbWFpbCk7XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgTUZBIGlzIHJlcXVpcmVkXHJcbiAgICBpZiAodXNlci5tZmFfZW5hYmxlZCAmJiB1c2VyLm1mYV9zZWNyZXQpIHtcclxuICAgICAgLy8gUmV0dXJuIHN1Y2Nlc3MgYnV0IGluZGljYXRlIE1GQSBpcyByZXF1aXJlZFxyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgcmVxdWlyZXNNRkE6IHRydWUsXHJcbiAgICAgICAgdXNlcjoge1xyXG4gICAgICAgICAgaWQ6IHVzZXIuaWQsXHJcbiAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICAgIHJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgICAgIGZpcnN0TmFtZTogdXNlci5maXJzdF9uYW1lLFxyXG4gICAgICAgICAgbGFzdE5hbWU6IHVzZXIubGFzdF9uYW1lLFxyXG4gICAgICAgICAgaXNBY3RpdmU6IHVzZXIuaXNfYWN0aXZlLFxyXG4gICAgICAgICAgbWZhRW5hYmxlZDogdXNlci5tZmFfZW5hYmxlZCxcclxuICAgICAgICAgIGxhc3RBY3Rpdml0eTogRGF0ZS5ub3coKSxcclxuICAgICAgICAgIHBlcm1pc3Npb25zOiB1c2VyLnBlcm1pc3Npb25zIHx8IFtdXHJcbiAgICAgICAgfVxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEdlbmVyYXRlIEpXVCB0b2tlblxyXG4gICAgY29uc3Qgand0U2VjcmV0ID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAncGxhY2Vob2xkZXItc2VjcmV0JztcclxuICAgIGNvbnN0IHRva2VuID0gand0LnNpZ24oXHJcbiAgICAgIHtcclxuICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXHJcbiAgICAgICAgZW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgICAgcm9sZTogdXNlci5yb2xlXHJcbiAgICAgIH0sXHJcbiAgICAgIGp3dFNlY3JldCxcclxuICAgICAgeyBleHBpcmVzSW46ICc4aCcgfVxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBVcGRhdGUgbGFzdCBsb2dpblxyXG4gICAgYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oJ2FkbWluX3VzZXJzJylcclxuICAgICAgLnVwZGF0ZSh7XHJcbiAgICAgICAgbGFzdF9sb2dpbl9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgIGxhc3RfYWN0aXZpdHk6IERhdGUubm93KClcclxuICAgICAgfSlcclxuICAgICAgLmVxKCdpZCcsIHVzZXIuaWQpO1xyXG5cclxuICAgIGF3YWl0IGF1ZGl0TG9nKHtcclxuICAgICAgYWN0aW9uOiAnTE9HSU5fU1VDQ0VTUycsXHJcbiAgICAgIHVzZXJJZDogdXNlci5pZCxcclxuICAgICAgdXNlclJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgZW1haWwsXHJcbiAgICAgIGlwXHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICB0b2tlbixcclxuICAgICAgdXNlcjoge1xyXG4gICAgICAgIGlkOiB1c2VyLmlkLFxyXG4gICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICAgIHJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgICBmaXJzdE5hbWU6IHVzZXIuZmlyc3RfbmFtZSxcclxuICAgICAgICBsYXN0TmFtZTogdXNlci5sYXN0X25hbWUsXHJcbiAgICAgICAgaXNBY3RpdmU6IHVzZXIuaXNfYWN0aXZlLFxyXG4gICAgICAgIG1mYUVuYWJsZWQ6IHVzZXIubWZhX2VuYWJsZWQsXHJcbiAgICAgICAgbGFzdEFjdGl2aXR5OiBEYXRlLm5vdygpLFxyXG4gICAgICAgIHBlcm1pc3Npb25zOiB1c2VyLnBlcm1pc3Npb25zIHx8IFtdXHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0FkbWluIGxvZ2luIGVycm9yOicsIGVycm9yKTtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0xvZ2luIGZhaWxlZCcgfTtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBWZXJpZnkgTUZBIHRva2VuIGFuZCBjb21wbGV0ZSBsb2dpblxyXG4gKiBOb3RlOiBUaGlzIGZ1bmN0aW9uIHJlcXVpcmVzIE5vZGUuanMgcnVudGltZSBkdWUgdG8gc3BlYWtlYXN5IGRlcGVuZGVuY3lcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2ZXJpZnlNRkFBbmRMb2dpbihcclxuICB1c2VySWQ6IHN0cmluZyxcclxuICBtZmFUb2tlbjogc3RyaW5nLFxyXG4gIGlwPzogc3RyaW5nXHJcbik6IFByb21pc2U8TG9naW5SZXN1bHQ+IHtcclxuICAvLyBJbXBvcnQgc3BlYWtlYXN5IGR5bmFtaWNhbGx5IHRvIGF2b2lkIEVkZ2UgUnVudGltZSBpc3N1ZXNcclxuICBjb25zdCBzcGVha2Vhc3kgPSBhd2FpdCBpbXBvcnQoJ3NwZWFrZWFzeScpO1xyXG5cclxuICB0cnkge1xyXG4gICAgY29uc3QgeyBkYXRhOiB1c2VyLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oJ2FkbWluX3VzZXJzJylcclxuICAgICAgLnNlbGVjdChgXHJcbiAgICAgICAgaWQsXHJcbiAgICAgICAgZW1haWwsXHJcbiAgICAgICAgcm9sZSxcclxuICAgICAgICBmaXJzdF9uYW1lLFxyXG4gICAgICAgIGxhc3RfbmFtZSxcclxuICAgICAgICBpc19hY3RpdmUsXHJcbiAgICAgICAgbWZhX2VuYWJsZWQsXHJcbiAgICAgICAgbWZhX3NlY3JldCxcclxuICAgICAgICBwZXJtaXNzaW9uc1xyXG4gICAgICBgKVxyXG4gICAgICAuZXEoJ2lkJywgdXNlcklkKVxyXG4gICAgICAuc2luZ2xlKCk7XHJcblxyXG4gICAgaWYgKGVycm9yIHx8ICF1c2VyIHx8ICF1c2VyLm1mYV9zZWNyZXQpIHtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnSW52YWxpZCBNRkEgc2V0dXAnIH07XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVmVyaWZ5IE1GQSB0b2tlblxyXG4gICAgY29uc3QgdmVyaWZpZWQgPSBzcGVha2Vhc3kudG90cC52ZXJpZnkoe1xyXG4gICAgICBzZWNyZXQ6IHVzZXIubWZhX3NlY3JldCxcclxuICAgICAgZW5jb2Rpbmc6ICdiYXNlMzInLFxyXG4gICAgICB0b2tlbjogbWZhVG9rZW4sXHJcbiAgICAgIHdpbmRvdzogMlxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCF2ZXJpZmllZCkge1xyXG4gICAgICBhd2FpdCBhdWRpdExvZyh7XHJcbiAgICAgICAgYWN0aW9uOiAnTUZBX0ZBSUxFRCcsXHJcbiAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxyXG4gICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICAgIGlwLFxyXG4gICAgICAgIHJlYXNvbjogJ0ludmFsaWQgTUZBIHRva2VuJ1xyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnSW52YWxpZCBNRkEgdG9rZW4nIH07XHJcbiAgICB9XHJcblxyXG4gICAgLy8gR2VuZXJhdGUgSldUIHRva2VuXHJcbiAgICBjb25zdCBqd3RTZWNyZXQgPSBwcm9jZXNzLmVudi5KV1RfU0VDUkVUIHx8ICdwbGFjZWhvbGRlci1zZWNyZXQnO1xyXG4gICAgY29uc3QgdG9rZW4gPSBqd3Quc2lnbihcclxuICAgICAge1xyXG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcclxuICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICByb2xlOiB1c2VyLnJvbGVcclxuICAgICAgfSxcclxuICAgICAgand0U2VjcmV0LFxyXG4gICAgICB7IGV4cGlyZXNJbjogJzhoJyB9XHJcbiAgICApO1xyXG5cclxuICAgIC8vIFVwZGF0ZSBsYXN0IGxvZ2luXHJcbiAgICBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbSgnYWRtaW5fdXNlcnMnKVxyXG4gICAgICAudXBkYXRlKHtcclxuICAgICAgICBsYXN0X2xvZ2luX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgbGFzdF9hY3Rpdml0eTogRGF0ZS5ub3coKVxyXG4gICAgICB9KVxyXG4gICAgICAuZXEoJ2lkJywgdXNlci5pZCk7XHJcblxyXG4gICAgYXdhaXQgYXVkaXRMb2coe1xyXG4gICAgICBhY3Rpb246ICdNRkFfTE9HSU5fU1VDQ0VTUycsXHJcbiAgICAgIHVzZXJJZDogdXNlci5pZCxcclxuICAgICAgdXNlclJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgZW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgIGlwXHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICB0b2tlbixcclxuICAgICAgdXNlcjoge1xyXG4gICAgICAgIGlkOiB1c2VyLmlkLFxyXG4gICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICAgIHJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgICBmaXJzdE5hbWU6IHVzZXIuZmlyc3RfbmFtZSxcclxuICAgICAgICBsYXN0TmFtZTogdXNlci5sYXN0X25hbWUsXHJcbiAgICAgICAgaXNBY3RpdmU6IHVzZXIuaXNfYWN0aXZlLFxyXG4gICAgICAgIG1mYUVuYWJsZWQ6IHVzZXIubWZhX2VuYWJsZWQsXHJcbiAgICAgICAgbGFzdEFjdGl2aXR5OiBEYXRlLm5vdygpLFxyXG4gICAgICAgIHBlcm1pc3Npb25zOiB1c2VyLnBlcm1pc3Npb25zIHx8IFtdXHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ01GQSB2ZXJpZmljYXRpb24gZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnTUZBIHZlcmlmaWNhdGlvbiBmYWlsZWQnIH07XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogR2VuZXJhdGUgTUZBIHNlY3JldCBmb3IgdXNlclxyXG4gKiBOb3RlOiBUaGlzIGZ1bmN0aW9uIHJlcXVpcmVzIE5vZGUuanMgcnVudGltZSBkdWUgdG8gc3BlYWtlYXN5IGRlcGVuZGVuY3lcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZU1GQVNlY3JldCh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8eyBzZWNyZXQ6IHN0cmluZzsgcXJDb2RlOiBzdHJpbmcgfSB8IG51bGw+IHtcclxuICAvLyBJbXBvcnQgc3BlYWtlYXN5IGR5bmFtaWNhbGx5IHRvIGF2b2lkIEVkZ2UgUnVudGltZSBpc3N1ZXNcclxuICBjb25zdCBzcGVha2Vhc3kgPSBhd2FpdCBpbXBvcnQoJ3NwZWFrZWFzeScpO1xyXG5cclxuICB0cnkge1xyXG4gICAgY29uc3QgeyBkYXRhOiB1c2VyIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbSgnYWRtaW5fdXNlcnMnKVxyXG4gICAgICAuc2VsZWN0KCdlbWFpbCwgZmlyc3RfbmFtZSwgbGFzdF9uYW1lJylcclxuICAgICAgLmVxKCdpZCcsIHVzZXJJZClcclxuICAgICAgLnNpbmdsZSgpO1xyXG5cclxuICAgIGlmICghdXNlcikgcmV0dXJuIG51bGw7XHJcblxyXG4gICAgY29uc3Qgc2VjcmV0ID0gc3BlYWtlYXN5LmdlbmVyYXRlU2VjcmV0KHtcclxuICAgICAgbmFtZTogYCR7dXNlci5maXJzdF9uYW1lfSAke3VzZXIubGFzdF9uYW1lfWAsXHJcbiAgICAgIGlzc3VlcjogJ09jZWFuIFNvdWwgU3BhcmtsZXMgQWRtaW4nLFxyXG4gICAgICBsZW5ndGg6IDMyXHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzZWNyZXQ6IHNlY3JldC5iYXNlMzIsXHJcbiAgICAgIHFyQ29kZTogc2VjcmV0Lm90cGF1dGhfdXJsIVxyXG4gICAgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignTUZBIHNlY3JldCBnZW5lcmF0aW9uIGVycm9yOicsIGVycm9yKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEVuYWJsZSBNRkEgZm9yIHVzZXJcclxuICogTm90ZTogVGhpcyBmdW5jdGlvbiByZXF1aXJlcyBOb2RlLmpzIHJ1bnRpbWUgZHVlIHRvIHNwZWFrZWFzeSBkZXBlbmRlbmN5XHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZW5hYmxlTUZBKHVzZXJJZDogc3RyaW5nLCBzZWNyZXQ6IHN0cmluZywgdG9rZW46IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xyXG4gIC8vIEltcG9ydCBzcGVha2Vhc3kgZHluYW1pY2FsbHkgdG8gYXZvaWQgRWRnZSBSdW50aW1lIGlzc3Vlc1xyXG4gIGNvbnN0IHNwZWFrZWFzeSA9IGF3YWl0IGltcG9ydCgnc3BlYWtlYXN5Jyk7XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyBWZXJpZnkgdGhlIHRva2VuIGZpcnN0XHJcbiAgICBjb25zdCB2ZXJpZmllZCA9IHNwZWFrZWFzeS50b3RwLnZlcmlmeSh7XHJcbiAgICAgIHNlY3JldCxcclxuICAgICAgZW5jb2Rpbmc6ICdiYXNlMzInLFxyXG4gICAgICB0b2tlbixcclxuICAgICAgd2luZG93OiAyXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXZlcmlmaWVkKSByZXR1cm4gZmFsc2U7XHJcblxyXG4gICAgLy8gU2F2ZSBNRkEgc2VjcmV0IHRvIGRhdGFiYXNlXHJcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbSgnYWRtaW5fdXNlcnMnKVxyXG4gICAgICAudXBkYXRlKHtcclxuICAgICAgICBtZmFfc2VjcmV0OiBzZWNyZXQsXHJcbiAgICAgICAgbWZhX2VuYWJsZWQ6IHRydWVcclxuICAgICAgfSlcclxuICAgICAgLmVxKCdpZCcsIHVzZXJJZCk7XHJcblxyXG4gICAgaWYgKGVycm9yKSByZXR1cm4gZmFsc2U7XHJcblxyXG4gICAgYXdhaXQgYXVkaXRMb2coe1xyXG4gICAgICBhY3Rpb246ICdNRkFfRU5BQkxFRCcsXHJcbiAgICAgIHVzZXJJZCxcclxuICAgICAgcmVhc29uOiAnVXNlciBlbmFibGVkIE1GQSdcclxuICAgIH0pO1xyXG5cclxuICAgIHJldHVybiB0cnVlO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdNRkEgZW5hYmxlIGVycm9yOicsIGVycm9yKTtcclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBSZWNvcmQgZmFpbGVkIGxvZ2luIGF0dGVtcHRcclxuICovXHJcbmFzeW5jIGZ1bmN0aW9uIHJlY29yZEZhaWxlZEF0dGVtcHQoZW1haWw6IHN0cmluZywgaXA/OiBzdHJpbmcsIHJlYXNvbj86IHN0cmluZykge1xyXG4gIGF3YWl0IHN1cGFiYXNlXHJcbiAgICAuZnJvbSgnbG9naW5fYXR0ZW1wdHMnKVxyXG4gICAgLmluc2VydCh7XHJcbiAgICAgIGVtYWlsLFxyXG4gICAgICBpcF9hZGRyZXNzOiBpcCxcclxuICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgIHJlYXNvbixcclxuICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXHJcbiAgICB9KTtcclxuXHJcbiAgYXdhaXQgYXVkaXRMb2coe1xyXG4gICAgYWN0aW9uOiAnTE9HSU5fRkFJTEVEJyxcclxuICAgIGVtYWlsLFxyXG4gICAgaXAsXHJcbiAgICByZWFzb25cclxuICB9KTtcclxufVxyXG5cclxuLyoqXHJcbiAqIEFkbWluIGxvZ291dFxyXG4gKi9cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGFkbWluTG9nb3V0KHVzZXJJZDogc3RyaW5nLCBpcD86IHN0cmluZykge1xyXG4gIHRyeSB7XHJcbiAgICBhd2FpdCBhdWRpdExvZyh7XHJcbiAgICAgIGFjdGlvbjogJ0xPR09VVCcsXHJcbiAgICAgIHVzZXJJZCxcclxuICAgICAgaXBcclxuICAgIH0pO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdMb2dvdXQgYXVkaXQgZXJyb3I6JywgZXJyb3IpO1xyXG4gIH1cclxufVxyXG4iXSwibmFtZXMiOlsiand0IiwiYmNyeXB0IiwiY3JlYXRlQ2xpZW50IiwiYXVkaXRMb2ciLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZUtleSIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJzdXBhYmFzZSIsInZlcmlmeUFkbWluVG9rZW4iLCJ0b2tlbiIsImp3dFNlY3JldCIsIkpXVF9TRUNSRVQiLCJkZWNvZGVkIiwidmVyaWZ5IiwiZGF0YSIsInVzZXIiLCJlcnJvciIsImZyb20iLCJzZWxlY3QiLCJlcSIsInVzZXJJZCIsInNpbmdsZSIsInZhbGlkIiwiaXNfYWN0aXZlIiwidXBkYXRlIiwibGFzdF9hY3Rpdml0eSIsIkRhdGUiLCJub3ciLCJpZCIsImVtYWlsIiwicm9sZSIsImZpcnN0TmFtZSIsImZpcnN0X25hbWUiLCJsYXN0TmFtZSIsImxhc3RfbmFtZSIsImlzQWN0aXZlIiwibWZhRW5hYmxlZCIsIm1mYV9lbmFibGVkIiwibGFzdEFjdGl2aXR5IiwicGVybWlzc2lvbnMiLCJhZG1pbkxvZ2luIiwicGFzc3dvcmQiLCJpcCIsImF0dGVtcHRzIiwiZ3RlIiwidG9JU09TdHJpbmciLCJvcmRlciIsImFzY2VuZGluZyIsImxlbmd0aCIsImFjdGlvbiIsInJlYXNvbiIsInN1Y2Nlc3MiLCJ0b0xvd2VyQ2FzZSIsInJlY29yZEZhaWxlZEF0dGVtcHQiLCJtZXNzYWdlIiwicGFzc3dvcmRWYWxpZCIsImNvbXBhcmUiLCJwYXNzd29yZF9oYXNoIiwiZGVsZXRlIiwibWZhX3NlY3JldCIsInJlcXVpcmVzTUZBIiwic2lnbiIsImV4cGlyZXNJbiIsImxhc3RfbG9naW5fYXQiLCJ1c2VyUm9sZSIsImNvbnNvbGUiLCJ2ZXJpZnlNRkFBbmRMb2dpbiIsIm1mYVRva2VuIiwic3BlYWtlYXN5IiwidmVyaWZpZWQiLCJ0b3RwIiwic2VjcmV0IiwiZW5jb2RpbmciLCJ3aW5kb3ciLCJnZW5lcmF0ZU1GQVNlY3JldCIsImdlbmVyYXRlU2VjcmV0IiwibmFtZSIsImlzc3VlciIsImJhc2UzMiIsInFyQ29kZSIsIm90cGF1dGhfdXJsIiwiZW5hYmxlTUZBIiwiaW5zZXJ0IiwiaXBfYWRkcmVzcyIsImNyZWF0ZWRfYXQiLCJhZG1pbkxvZ291dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/auth/admin-auth.ts\n");

/***/ }),

/***/ "(api)/./lib/security/audit-logging.ts":
/*!***************************************!*\
  !*** ./lib/security/audit-logging.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditActions: () => (/* binding */ AuditActions),\n/* harmony export */   auditLog: () => (/* binding */ auditLog),\n/* harmony export */   exportAuditLogs: () => (/* binding */ exportAuditLogs),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs),\n/* harmony export */   logCriticalEvent: () => (/* binding */ logCriticalEvent),\n/* harmony export */   logDataChange: () => (/* binding */ logDataChange),\n/* harmony export */   logSecurityEvent: () => (/* binding */ logSecurityEvent),\n/* harmony export */   logUserAction: () => (/* binding */ logUserAction)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Log audit event to database and console\r\n */ async function auditLog(entry) {\n    try {\n        const logEntry = {\n            action: entry.action,\n            user_id: entry.userId,\n            user_role: entry.userRole,\n            email: entry.email,\n            ip_address: entry.ip,\n            path: entry.path,\n            resource: entry.resource,\n            resource_id: entry.resourceId,\n            old_values: entry.oldValues,\n            new_values: entry.newValues,\n            reason: entry.reason,\n            error: entry.error,\n            metadata: entry.metadata,\n            severity: entry.severity || \"medium\",\n            created_at: new Date().toISOString()\n        };\n        // Log to database\n        const { error } = await supabase.from(\"audit_logs\").insert(logEntry);\n        if (error) {\n            console.error(\"Failed to write audit log to database:\", error);\n        }\n        // Log to console for immediate visibility\n        const logLevel = getLogLevel(entry.severity || \"medium\");\n        const logMessage = formatLogMessage(entry);\n        console[logLevel](logMessage);\n        // For critical events, also send alerts\n        if (entry.severity === \"critical\") {\n            await sendCriticalAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Audit logging failed:\", error);\n        // Fallback to console logging\n        console.error(\"AUDIT_LOG_FAILURE:\", JSON.stringify(entry, null, 2));\n    }\n}\n/**\r\n * Get appropriate console log level based on severity\r\n */ function getLogLevel(severity) {\n    switch(severity){\n        case \"low\":\n            return \"log\";\n        case \"medium\":\n            return \"log\";\n        case \"high\":\n            return \"warn\";\n        case \"critical\":\n            return \"error\";\n        default:\n            return \"log\";\n    }\n}\n/**\r\n * Format audit log message for console output\r\n */ function formatLogMessage(entry) {\n    const timestamp = new Date().toISOString();\n    const user = entry.userId ? `[User: ${entry.userId}]` : \"\";\n    const ip = entry.ip ? `[IP: ${entry.ip}]` : \"\";\n    const path = entry.path ? `[Path: ${entry.path}]` : \"\";\n    return `[AUDIT] ${timestamp} ${entry.action} ${user} ${ip} ${path} ${entry.reason || \"\"}`.trim();\n}\n/**\r\n * Send critical alert for high-severity events\r\n */ async function sendCriticalAlert(entry) {\n    try {\n        // In production, this would send alerts via:\n        // - Email to admin team\n        // - Slack/Discord webhook\n        // - SMS for critical security events\n        // - Push notifications\n        console.error(\"\\uD83D\\uDEA8 CRITICAL SECURITY EVENT:\", {\n            action: entry.action,\n            userId: entry.userId,\n            ip: entry.ip,\n            reason: entry.reason,\n            timestamp: new Date().toISOString()\n        });\n        // Example: Send email alert (implement based on your email service)\n        if (process.env.ENABLE_CRITICAL_ALERTS === \"true\") {\n            await sendEmailAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Failed to send critical alert:\", error);\n    }\n}\n/**\r\n * Send email alert for critical events\r\n */ async function sendEmailAlert(entry) {\n    // Implementation would depend on your email service\n    // This is a placeholder for the actual implementation\n    console.log(\"Email alert would be sent for:\", entry.action);\n}\n/**\r\n * Audit log helper functions for common actions\r\n */ const AuditActions = {\n    // Authentication events\n    LOGIN_SUCCESS: \"LOGIN_SUCCESS\",\n    LOGIN_FAILED: \"LOGIN_FAILED\",\n    LOGIN_BLOCKED: \"LOGIN_BLOCKED\",\n    LOGOUT: \"LOGOUT\",\n    MFA_ENABLED: \"MFA_ENABLED\",\n    MFA_DISABLED: \"MFA_DISABLED\",\n    MFA_FAILED: \"MFA_FAILED\",\n    PASSWORD_CHANGED: \"PASSWORD_CHANGED\",\n    PASSWORD_RESET: \"PASSWORD_RESET\",\n    // Access control events\n    ACCESS_GRANTED: \"ACCESS_GRANTED\",\n    ACCESS_DENIED: \"ACCESS_DENIED\",\n    UNAUTHORIZED_ACCESS: \"UNAUTHORIZED_ACCESS\",\n    INSUFFICIENT_PERMISSIONS: \"INSUFFICIENT_PERMISSIONS\",\n    SESSION_TIMEOUT: \"SESSION_TIMEOUT\",\n    // Data modification events\n    RECORD_CREATED: \"RECORD_CREATED\",\n    RECORD_UPDATED: \"RECORD_UPDATED\",\n    RECORD_DELETED: \"RECORD_DELETED\",\n    BULK_UPDATE: \"BULK_UPDATE\",\n    BULK_DELETE: \"BULK_DELETE\",\n    // Admin actions\n    USER_CREATED: \"USER_CREATED\",\n    USER_UPDATED: \"USER_UPDATED\",\n    USER_DEACTIVATED: \"USER_DEACTIVATED\",\n    ROLE_CHANGED: \"ROLE_CHANGED\",\n    PERMISSIONS_CHANGED: \"PERMISSIONS_CHANGED\",\n    // System events\n    SYSTEM_ERROR: \"SYSTEM_ERROR\",\n    CONFIGURATION_CHANGED: \"CONFIGURATION_CHANGED\",\n    BACKUP_CREATED: \"BACKUP_CREATED\",\n    BACKUP_RESTORED: \"BACKUP_RESTORED\",\n    // Security events\n    IP_BLOCKED: \"IP_BLOCKED\",\n    RATE_LIMITED: \"RATE_LIMITED\",\n    SUSPICIOUS_ACTIVITY: \"SUSPICIOUS_ACTIVITY\",\n    SECURITY_SCAN: \"SECURITY_SCAN\",\n    VULNERABILITY_DETECTED: \"VULNERABILITY_DETECTED\",\n    // Business events\n    BOOKING_CREATED: \"BOOKING_CREATED\",\n    BOOKING_MODIFIED: \"BOOKING_MODIFIED\",\n    BOOKING_CANCELLED: \"BOOKING_CANCELLED\",\n    PAYMENT_PROCESSED: \"PAYMENT_PROCESSED\",\n    REFUND_ISSUED: \"REFUND_ISSUED\"\n};\n/**\r\n * Helper function to log user actions\r\n */ async function logUserAction(action, userId, userRole, details = {}) {\n    await auditLog({\n        action,\n        userId,\n        userRole,\n        severity: \"medium\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log security events\r\n */ async function logSecurityEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"high\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log critical security events\r\n */ async function logCriticalEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"critical\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log data changes\r\n */ async function logDataChange(action, userId, resource, resourceId, oldValues, newValues) {\n    await auditLog({\n        action,\n        userId,\n        resource,\n        resourceId,\n        oldValues,\n        newValues,\n        severity: \"medium\"\n    });\n}\n/**\r\n * Get audit logs with filtering and pagination\r\n */ async function getAuditLogs(filters) {\n    try {\n        let query = supabase.from(\"audit_logs\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        if (filters.userId) {\n            query = query.eq(\"user_id\", filters.userId);\n        }\n        if (filters.action) {\n            query = query.eq(\"action\", filters.action);\n        }\n        if (filters.severity) {\n            query = query.eq(\"severity\", filters.severity);\n        }\n        if (filters.startDate) {\n            query = query.gte(\"created_at\", filters.startDate);\n        }\n        if (filters.endDate) {\n            query = query.lte(\"created_at\", filters.endDate);\n        }\n        if (filters.limit) {\n            query = query.limit(filters.limit);\n        }\n        if (filters.offset) {\n            query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw error;\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error fetching audit logs:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n/**\r\n * Export audit logs for compliance\r\n */ async function exportAuditLogs(startDate, endDate, format = \"json\") {\n    try {\n        const { data, error } = await supabase.from(\"audit_logs\").select(\"*\").gte(\"created_at\", startDate).lte(\"created_at\", endDate).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) {\n            throw error;\n        }\n        if (format === \"csv\") {\n            return convertToCSV(data);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Error exporting audit logs:\", error);\n        throw error;\n    }\n}\n/**\r\n * Convert audit logs to CSV format\r\n */ function convertToCSV(data) {\n    if (!data || data.length === 0) {\n        return \"\";\n    }\n    const headers = Object.keys(data[0]);\n    const csvContent = [\n        headers.join(\",\"),\n        ...data.map((row)=>headers.map((header)=>{\n                const value = row[header];\n                if (typeof value === \"object\" && value !== null) {\n                    return `\"${JSON.stringify(value).replace(/\"/g, '\"\"')}\"`;\n                }\n                return `\"${String(value || \"\").replace(/\"/g, '\"\"')}\"`;\n            }).join(\",\"))\n    ].join(\"\\n\");\n    return csvContent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/security/audit-logging.ts\n");

/***/ }),

/***/ "(api)/./lib/security/rate-limiting.ts":
/*!***************************************!*\
  !*** ./lib/security/rate-limiting.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdvancedRateLimiter: () => (/* binding */ AdvancedRateLimiter),\n/* harmony export */   addRateLimit: () => (/* binding */ addRateLimit),\n/* harmony export */   getAllRateLimits: () => (/* binding */ getAllRateLimits),\n/* harmony export */   getRateLimitStatus: () => (/* binding */ getRateLimitStatus),\n/* harmony export */   rateLimitCheck: () => (/* binding */ rateLimitCheck),\n/* harmony export */   removeRateLimit: () => (/* binding */ removeRateLimit),\n/* harmony export */   resetRateLimit: () => (/* binding */ resetRateLimit)\n/* harmony export */ });\n// In-memory store for rate limiting (in production, use Redis)\nconst rateLimitStore = new Map();\n// Different rate limits for different endpoints\nconst RATE_LIMITS = {\n    \"/api/auth/login\": {\n        windowMs: 15 * 60 * 1000,\n        maxRequests:  true ? 50 : 0\n    },\n    \"/api/auth/mfa\": {\n        windowMs: 5 * 60 * 1000,\n        maxRequests:  true ? 50 : 0\n    },\n    \"/api/auth/forgot-password\": {\n        windowMs: 60 * 60 * 1000,\n        maxRequests:  true ? 20 : 0\n    },\n    \"/api/admin\": {\n        windowMs: 60 * 1000,\n        maxRequests:  true ? 1000 : 0\n    },\n    \"default\": {\n        windowMs: 60 * 1000,\n        maxRequests:  true ? 600 : 0\n    }\n};\n/**\r\n * Check rate limit for incoming request (supports both NextRequest and NextApiRequest)\r\n */ async function rateLimitCheck(request) {\n    const ip = getClientIP(request);\n    const path = request.nextUrl?.pathname || request.url || \"/unknown\";\n    // Skip rate limiting for localhost in development\n    if ( true && (ip === \"::1\" || ip === \"127.0.0.1\" || ip === \"unknown\")) {\n        return {\n            allowed: true,\n            ip,\n            remaining: 999999,\n            resetTime: Date.now() + 60000\n        };\n    }\n    // Get rate limit config for this path\n    const config = getRateLimitConfig(path);\n    // Create key for this IP and path combination\n    const key = `${ip}:${path}`;\n    const now = Date.now();\n    const windowStart = now - config.windowMs;\n    // Get current count for this key\n    let record = rateLimitStore.get(key);\n    // Clean up expired records\n    if (record && record.resetTime < now) {\n        rateLimitStore.delete(key);\n        record = undefined;\n    }\n    // Initialize record if it doesn't exist\n    if (!record) {\n        record = {\n            count: 0,\n            resetTime: now + config.windowMs\n        };\n    }\n    // Check if limit exceeded\n    if (record.count >= config.maxRequests) {\n        return {\n            allowed: false,\n            ip,\n            remaining: 0,\n            resetTime: record.resetTime,\n            reason: \"Rate limit exceeded\"\n        };\n    }\n    // Increment count\n    record.count++;\n    rateLimitStore.set(key, record);\n    // Clean up old entries periodically\n    if (Math.random() < 0.01) {\n        cleanupExpiredEntries();\n    }\n    return {\n        allowed: true,\n        ip,\n        remaining: config.maxRequests - record.count,\n        resetTime: record.resetTime\n    };\n}\n/**\r\n * Get rate limit configuration for a specific path\r\n */ function getRateLimitConfig(path) {\n    // Check for exact matches first\n    if (RATE_LIMITS[path]) {\n        return RATE_LIMITS[path];\n    }\n    // Check for pattern matches\n    for (const [pattern, config] of Object.entries(RATE_LIMITS)){\n        if (pattern !== \"default\" && path.startsWith(pattern)) {\n            return config;\n        }\n    }\n    // Return default config\n    return RATE_LIMITS.default;\n}\n/**\r\n * Get client IP address from request (compatible with both NextRequest and NextApiRequest)\r\n */ function getClientIP(request) {\n    // Handle NextRequest (middleware)\n    if (request.headers && typeof request.headers.get === \"function\") {\n        const forwarded = request.headers.get(\"x-forwarded-for\");\n        const realIP = request.headers.get(\"x-real-ip\");\n        const cfConnectingIP = request.headers.get(\"cf-connecting-ip\");\n        if (cfConnectingIP) {\n            return cfConnectingIP;\n        }\n        if (forwarded) {\n            return forwarded.split(\",\")[0].trim();\n        }\n        if (realIP) {\n            return realIP;\n        }\n        return request.ip || \"unknown\";\n    }\n    // Handle NextApiRequest (API routes)\n    if (request.headers && typeof request.headers === \"object\") {\n        const forwarded = request.headers[\"x-forwarded-for\"];\n        const realIP = request.headers[\"x-real-ip\"];\n        const cfConnectingIP = request.headers[\"cf-connecting-ip\"];\n        if (cfConnectingIP) {\n            return Array.isArray(cfConnectingIP) ? cfConnectingIP[0] : cfConnectingIP;\n        }\n        if (forwarded) {\n            return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(\",\")[0].trim();\n        }\n        if (realIP) {\n            return Array.isArray(realIP) ? realIP[0] : realIP;\n        }\n    }\n    return \"unknown\";\n}\n/**\r\n * Clean up expired entries from rate limit store\r\n */ function cleanupExpiredEntries() {\n    const now = Date.now();\n    const keysToDelete = [];\n    rateLimitStore.forEach((record, key)=>{\n        if (record.resetTime < now) {\n            keysToDelete.push(key);\n        }\n    });\n    keysToDelete.forEach((key)=>rateLimitStore.delete(key));\n}\n/**\r\n * Reset rate limit for specific IP (admin function)\r\n */ async function resetRateLimit(ip, path) {\n    try {\n        if (path) {\n            const key = `${ip}:${path}`;\n            rateLimitStore.delete(key);\n        } else {\n            // Reset all limits for this IP\n            const keysToReset = [];\n            rateLimitStore.forEach((_, key)=>{\n                if (key.startsWith(`${ip}:`)) {\n                    keysToReset.push(key);\n                }\n            });\n            keysToReset.forEach((key)=>rateLimitStore.delete(key));\n        }\n        console.log(`Rate limit reset for IP: ${ip}${path ? ` on path: ${path}` : \"\"}`);\n        return true;\n    } catch (error) {\n        console.error(\"Error resetting rate limit:\", error);\n        return false;\n    }\n}\n/**\r\n * Get current rate limit status for IP\r\n */ async function getRateLimitStatus(ip, path) {\n    const config = getRateLimitConfig(path);\n    const key = `${ip}:${path}`;\n    const record = rateLimitStore.get(key);\n    if (!record || record.resetTime < Date.now()) {\n        return {\n            count: 0,\n            limit: config.maxRequests,\n            remaining: config.maxRequests,\n            resetTime: Date.now() + config.windowMs\n        };\n    }\n    return {\n        count: record.count,\n        limit: config.maxRequests,\n        remaining: Math.max(0, config.maxRequests - record.count),\n        resetTime: record.resetTime\n    };\n}\n/**\r\n * Add custom rate limit for specific endpoint\r\n */ function addRateLimit(path, config) {\n    RATE_LIMITS[path] = config;\n}\n/**\r\n * Remove rate limit for specific endpoint\r\n */ function removeRateLimit(path) {\n    if (path === \"default\") {\n        return false; // Cannot remove default\n    }\n    return delete RATE_LIMITS[path];\n}\n/**\r\n * Get all current rate limit configurations\r\n */ function getAllRateLimits() {\n    return {\n        ...RATE_LIMITS\n    };\n}\n/**\r\n * Advanced rate limiting with different strategies\r\n */ class AdvancedRateLimiter {\n    /**\r\n   * Sliding window rate limiter\r\n   */ async slidingWindow(key, windowMs, maxRequests) {\n        const now = Date.now();\n        const windowStart = now - windowMs;\n        let requests = this.store.get(key) || [];\n        // Remove old requests outside the window\n        requests = requests.filter((timestamp)=>timestamp > windowStart);\n        if (requests.length >= maxRequests) {\n            return {\n                allowed: false,\n                ip: key,\n                remaining: 0,\n                resetTime: requests[0] + windowMs,\n                reason: \"Sliding window rate limit exceeded\"\n            };\n        }\n        // Add current request\n        requests.push(now);\n        this.store.set(key, requests);\n        return {\n            allowed: true,\n            ip: key,\n            remaining: maxRequests - requests.length,\n            resetTime: now + windowMs\n        };\n    }\n    /**\r\n   * Token bucket rate limiter\r\n   */ async tokenBucket(key, capacity, refillRate, tokensRequested = 1) {\n        const now = Date.now();\n        let bucket = this.store.get(key) || {\n            tokens: capacity,\n            lastRefill: now\n        };\n        // Calculate tokens to add based on time elapsed\n        const timePassed = now - bucket.lastRefill;\n        const tokensToAdd = Math.floor(timePassed * refillRate / 1000);\n        bucket.tokens = Math.min(capacity, bucket.tokens + tokensToAdd);\n        bucket.lastRefill = now;\n        if (bucket.tokens < tokensRequested) {\n            this.store.set(key, bucket);\n            return {\n                allowed: false,\n                ip: key,\n                remaining: bucket.tokens,\n                reason: \"Token bucket exhausted\"\n            };\n        }\n        bucket.tokens -= tokensRequested;\n        this.store.set(key, bucket);\n        return {\n            allowed: true,\n            ip: key,\n            remaining: bucket.tokens\n        };\n    }\n    constructor(){\n        this.store = new Map();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/security/rate-limiting.ts\n");

/***/ }),

/***/ "(api)/./pages/api/auth/login.ts":
/*!*********************************!*\
  !*** ./pages/api/auth/login.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/auth/admin-auth */ \"(api)/./lib/auth/admin-auth.ts\");\n/* harmony import */ var _lib_security_rate_limiting__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/security/rate-limiting */ \"(api)/./lib/security/rate-limiting.ts\");\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        // Rate limiting check\n        const rateLimitResult = await (0,_lib_security_rate_limiting__WEBPACK_IMPORTED_MODULE_1__.rateLimitCheck)(req);\n        if (!rateLimitResult.allowed) {\n            return res.status(429).json({\n                error: \"Too many login attempts. Please try again later.\",\n                resetTime: rateLimitResult.resetTime\n            });\n        }\n        const { email, password } = req.body;\n        // Validate input\n        if (!email || !password) {\n            return res.status(400).json({\n                error: \"Email and password are required\"\n            });\n        }\n        // Get client IP for audit logging\n        const clientIP = req.headers[\"x-forwarded-for\"] || req.headers[\"x-real-ip\"] || req.connection.remoteAddress || \"unknown\";\n        // Attempt login\n        const loginResult = await (0,_lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__.adminLogin)(email, password, clientIP);\n        if (!loginResult.success) {\n            return res.status(401).json({\n                error: loginResult.error\n            });\n        }\n        // Set secure cookie\n        if (loginResult.token) {\n            res.setHeader(\"Set-Cookie\", [\n                `admin-token=${loginResult.token}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=28800` // 8 hours\n            ]);\n        }\n        // Return response based on whether MFA is required\n        if (loginResult.requiresMFA) {\n            return res.status(200).json({\n                success: true,\n                requiresMFA: true,\n                user: loginResult.user\n            });\n        }\n        return res.status(200).json({\n            success: true,\n            token: loginResult.token,\n            user: loginResult.user\n        });\n    } catch (error) {\n        console.error(\"Login API error:\", error);\n        return res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/auth/login.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();