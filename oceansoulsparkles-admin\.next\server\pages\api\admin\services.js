"use strict";(()=>{var e={};e.id=483,e.ids=[483],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},9293:(e,r,t)=>{t.r(r),t.d(r,{config:()=>v,default:()=>p,routeModule:()=>_});var a={};t.r(a),t.d(a,{default:()=>l});var s=t(1802),i=t(7153),o=t(8781),n=t(7474),d=t(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,d.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function l(e,r){if("GET"!==e.method&&"POST"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let t=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!t)return r.status(401).json({error:"No authentication token"});let a=await (0,n.Wg)(t);if(!a.valid||!a.user)return r.status(401).json({error:"Invalid authentication"});let s=a.user;if("Admin"!==s.role&&"DEV"!==s.role)return r.status(403).json({error:"Insufficient permissions"});if("POST"===e.method){let{name:t,description:a,duration:s,price:i,category:o,status:n,visible_on_public:d,visible_on_pos:u,visible_on_events:l}=e.body;if(!t||!o)return r.status(400).json({error:"Name and category are required"});let{data:p,error:v}=await c.from("services").insert([{name:t,description:a,duration:s?parseInt(s):null,price:i?parseFloat(i):null,category:o,status:n||"active",visible_on_public:!1!==d,visible_on_pos:!1!==u,visible_on_events:!1!==l,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]).select().single();if(v)return console.error("Service creation error:",v),r.status(500).json({error:"Failed to create service"});return r.status(201).json({service:{...p,is_active:"active"===p.status,total_bookings:0}})}let{data:i,error:o}=await c.from("services").select(`
        id,
        name,
        description,
        duration,
        price,
        category,
        status,
        created_at,
        updated_at
      `).order("category",{ascending:!0}).order("name",{ascending:!0});if(o)return console.error("Services query error:",o),r.status(500).json({error:"Failed to fetch services"});let d=i?.map(e=>e.id)||[],{data:u}=await c.from("bookings").select("service_id").in("service_id",d),l=(u||[]).reduce((e,r)=>(e[r.service_id]=(e[r.service_id]||0)+1,e),{}),p=(i||[]).map(e=>({id:e.id,name:e.name,description:e.description,duration:e.duration,price:e.price,category:e.category,is_active:"active"===e.status,status:e.status,total_bookings:l[e.id]||0,created_at:e.created_at,updated_at:e.updated_at}));return r.status(200).json({services:p,total:p.length})}catch(e){return console.error("Services API error:",e),r.status(500).json({error:"Internal server error"})}}let p=(0,o.l)(a,"default"),v=(0,o.l)(a,"config"),_=new s.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/services",pathname:"/api/admin/services",bundlePath:"",filename:""},userland:a})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[805],()=>t(9293));module.exports=a})();