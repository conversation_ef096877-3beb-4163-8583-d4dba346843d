"use strict";(()=>{var e={};e.id=151,e.ids=[151],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},2163:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>_,routeModule:()=>m});var s={};r.r(s),r.d(s,{default:()=>d});var o=r(1802),a=r(7153),i=r(8781),n=r(7474),l=r(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function d(e,t){if("GET"!==e.method&&"POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!r)return t.status(401).json({error:"No authentication token"});let s=await (0,n.Wg)(r);if(!s.valid||!s.user)return t.status(401).json({error:"Invalid authentication"});let o=s.user;if("Admin"!==o.role&&"DEV"!==o.role)return t.status(403).json({error:"Insufficient permissions"});if("POST"===e.method){let{name:r,description:s,short_description:o,sku:a,price:i,sale_price:n,cost_price:l,category_name:u,image_url:d,gallery_images:_,is_active:p,stock:m,low_stock_threshold:f,reorder_point:h,reorder_quantity:g,max_stock_level:k,barcode:v,weight:y,dimensions:w,meta_title:I,meta_description:b,featured:j,abc_classification:q,seasonal_item:x,perishable:P,shelf_life_days:S,lead_time_days:E,minimum_order_quantity:A}=e.body;if(!r)return t.status(400).json({error:"Product name is required"});let{data:F,error:O}=await c.from("products").insert([{name:r,description:s,short_description:o,sku:a,price:i?parseFloat(i):null,sale_price:n?parseFloat(n):null,cost_price:l?parseFloat(l):null,category_name:u,image_url:d,gallery_images:_||[],is_active:!1!==p,stock:m?parseInt(m):0,low_stock_threshold:f?parseInt(f):5,reorder_point:h?parseInt(h):null,reorder_quantity:g?parseInt(g):null,max_stock_level:k?parseInt(k):null,barcode:v,weight:y?parseFloat(y):null,dimensions:w||null,meta_title:I,meta_description:b,featured:j||!1,abc_classification:q,seasonal_item:x||!1,perishable:P||!1,shelf_life_days:S?parseInt(S):null,lead_time_days:E?parseInt(E):null,minimum_order_quantity:A?parseInt(A):null,status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]).select().single();if(O)return console.error("Product creation error:",O),t.status(500).json({error:"Failed to create product"});return t.status(201).json({product:F})}let{data:a,error:i}=await c.from("products").select(`
        id,
        name,
        description,
        short_description,
        sku,
        price,
        sale_price,
        cost_price,
        category_name,
        image_url,
        gallery_images,
        is_active,
        stock,
        low_stock_threshold,
        reorder_point,
        reorder_quantity,
        max_stock_level,
        barcode,
        weight,
        dimensions,
        meta_title,
        meta_description,
        featured,
        abc_classification,
        seasonal_item,
        perishable,
        shelf_life_days,
        lead_time_days,
        minimum_order_quantity,
        status,
        created_at,
        updated_at
      `).order("name",{ascending:!0});if(i)return console.error("Products query error:",i),t.status(500).json({error:"Failed to fetch products"});let l=(a||[]).map(e=>({...e,stock_value:(e.price||0)*(e.stock||0),profit_margin:e.price&&e.cost_price?((e.price-e.cost_price)/e.price*100).toFixed(2):null,is_low_stock:(e.stock||0)<=(e.low_stock_threshold||5),is_out_of_stock:0>=(e.stock||0)}));return t.status(200).json({products:l,total:l.length,stats:{total_products:l.length,active_products:l.filter(e=>e.is_active).length,low_stock_products:l.filter(e=>e.is_low_stock).length,out_of_stock_products:l.filter(e=>e.is_out_of_stock).length,total_stock_value:l.reduce((e,t)=>e+(t.stock_value||0),0),featured_products:l.filter(e=>e.featured).length}})}catch(e){return console.error("Products API error:",e),t.status(500).json({error:"Internal server error"})}}let _=(0,i.l)(s,"default"),p=(0,i.l)(s,"config"),m=new o.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/products",pathname:"/api/admin/products",bundlePath:"",filename:""},userland:s})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[805],()=>r(2163));module.exports=s})();