(()=>{var e={};e.id=451,e.ids=[451,660],e.modules={1073:e=>{e.exports={posContainer:"POS_posContainer__6tGk7",header:"POS_header__NK5rM",title:"POS_title__aubSl",headerActions:"POS_headerActions__qWBFv",backButton:"POS_backButton__LBI7I",posLayout:"POS_posLayout__E66LX",servicesPanel:"POS_servicesPanel__5pgzh",serviceCategories:"POS_serviceCategories__GWW0s",categorySection:"POS_categorySection__VeYj5",categoryTitle:"POS_categoryTitle__4bPnN",serviceGrid:"POS_serviceGrid__ebdfY",serviceCard:"POS_serviceCard__895l8",serviceName:"POS_serviceName__HDmnI",servicePrice:"POS_servicePrice__8UUJz",serviceDuration:"POS_serviceDuration__9hI3C",cartPanel:"POS_cartPanel__6lBFL",customerSection:"POS_customerSection__59W3o",customerInfo:"POS_customerInfo__B3ZRB",changeCustomerBtn:"POS_changeCustomerBtn__E2veb",customerActions:"POS_customerActions__GZ3NQ",addCustomerBtn:"POS_addCustomerBtn__wIE0A",walkInBtn:"POS_walkInBtn__vNBwC",cartItems:"POS_cartItems__fuaCS",emptyCart:"POS_emptyCart__LyoKT",cartItem:"POS_cartItem__l0Fl8",itemInfo:"POS_itemInfo__qS4zm",itemName:"POS_itemName__rLI5l",itemPrice:"POS_itemPrice__iy5yI",itemControls:"POS_itemControls__EQzIb",quantityBtn:"POS_quantityBtn__MK9lD",quantity:"POS_quantity__tdGdn",removeBtn:"POS_removeBtn__AD3a2",paymentSection:"POS_paymentSection__j0cHT",total:"POS_total__xKac7",paymentMethods:"POS_paymentMethods__ov_uJ",processPaymentBtn:"POS_processPaymentBtn__C3Gwh",loadingContainer:"POS_loadingContainer__kkXgY",loadingSpinner:"POS_loadingSpinner__6iT1G",spin:"POS_spin__lN_rC"}},519:(e,s,a)=>{"use strict";a.a(e,async(e,t)=>{try{a.r(s),a.d(s,{config:()=>v,default:()=>u,getServerSideProps:()=>x,getStaticPaths:()=>p,getStaticProps:()=>h,reportWebVitals:()=>j,routeModule:()=>C,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>_,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>N});var r=a(7093),n=a(5244),i=a(1323),l=a(2899),c=a.n(l),o=a(6814),d=a(4679),m=e([o,d]);[o,d]=m.then?(await m)():m;let u=(0,i.l)(d,"default"),h=(0,i.l)(d,"getStaticProps"),p=(0,i.l)(d,"getStaticPaths"),x=(0,i.l)(d,"getServerSideProps"),v=(0,i.l)(d,"config"),j=(0,i.l)(d,"reportWebVitals"),N=(0,i.l)(d,"unstable_getStaticProps"),g=(0,i.l)(d,"unstable_getStaticPaths"),y=(0,i.l)(d,"unstable_getStaticParams"),S=(0,i.l)(d,"unstable_getServerProps"),_=(0,i.l)(d,"unstable_getServerSideProps"),C=new r.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/pos",pathname:"/admin/pos",bundlePath:"",filename:""},components:{App:o.default,Document:c()},userland:d});t()}catch(e){t(e)}})},2865:(e,s,a)=>{"use strict";a.d(s,{Z:()=>d});var t=a(997),r=a(6689),n=a(1073),i=a.n(n);function l({amount:e,onMethodSelect:s,onCancel:a}){let[n,l]=(0,r.useState)(null),[c,o]=(0,r.useState)(""),[d,m]=(0,r.useState)(!1),u=parseFloat(e||0),h=.029*u+.3,p=.026*u+.1,x=u+h,v=u+p,j=e=>{l(e),"cash"!==e&&(m(!0),s(e,{originalAmount:u,processingFee:"square_payment"===e?h:"square_terminal"===e?p:0,totalAmount:"square_payment"===e?x:"square_terminal"===e?v:u}))},N=e=>`$${e.toFixed(2)}`;return d?t.jsx("div",{className:i().paymentMethodContainer,children:(0,t.jsxs)("div",{className:i().processingPayment,children:[t.jsx("div",{className:i().loadingSpinner}),t.jsx("p",{children:"Processing payment..."})]})}):(0,t.jsxs)("div",{className:i().paymentMethodContainer,children:[(0,t.jsxs)("div",{className:i().paymentHeader,children:[t.jsx("h3",{children:"Select Payment Method"}),(0,t.jsxs)("div",{className:i().totalAmount,children:["Service Total: ",N(u)]})]}),(0,t.jsxs)("div",{className:i().paymentMethods,children:[(0,t.jsxs)("div",{className:`${i().paymentMethodCard} ${"square_payment"===n?i().selected:""}`,onClick:()=>j("square_payment"),children:[t.jsx("div",{className:i().methodIcon,children:"\uD83D\uDCB3"}),(0,t.jsxs)("div",{className:i().methodInfo,children:[t.jsx("h4",{children:"Card Payment"}),t.jsx("p",{children:"Credit/Debit Card Entry"}),t.jsx("div",{className:i().methodPricing,children:(0,t.jsxs)("div",{className:i().priceBreakdown,children:[(0,t.jsxs)("span",{children:["Service: ",N(u)]}),(0,t.jsxs)("span",{children:["Processing: ",N(h)]}),(0,t.jsxs)("strong",{children:["Total: ",N(x)]})]})})]}),t.jsx("div",{className:i().methodBadge,children:t.jsx("span",{children:"Online"})})]}),(0,t.jsxs)("div",{className:`${i().paymentMethodCard} ${"square_terminal"===n?i().selected:""}`,onClick:()=>j("square_terminal"),children:[t.jsx("div",{className:i().methodIcon,children:"\uD83D\uDCF1"}),(0,t.jsxs)("div",{className:i().methodInfo,children:[t.jsx("h4",{children:"Square Terminal"}),t.jsx("p",{children:"Hardware Card Reader"}),t.jsx("div",{className:i().methodPricing,children:(0,t.jsxs)("div",{className:i().priceBreakdown,children:[(0,t.jsxs)("span",{children:["Service: ",N(u)]}),(0,t.jsxs)("span",{children:["Processing: ",N(p)]}),(0,t.jsxs)("strong",{children:["Total: ",N(v)]})]})})]}),t.jsx("div",{className:i().methodBadge,children:t.jsx("span",{children:"Hardware"})})]}),(0,t.jsxs)("div",{className:`${i().paymentMethodCard} ${"cash"===n?i().selected:""}`,onClick:()=>l("cash"),children:[t.jsx("div",{className:i().methodIcon,children:"\uD83D\uDCB5"}),(0,t.jsxs)("div",{className:i().methodInfo,children:[t.jsx("h4",{children:"Cash Payment"}),t.jsx("p",{children:"Physical Currency"}),t.jsx("div",{className:i().methodPricing,children:(0,t.jsxs)("div",{className:i().priceBreakdown,children:[(0,t.jsxs)("span",{children:["Service: ",N(u)]}),(0,t.jsxs)("span",{children:["Processing: ",N(0)]}),(0,t.jsxs)("strong",{children:["Total: ",N(u)]})]})})]}),t.jsx("div",{className:i().methodBadge,children:t.jsx("span",{children:"No Fees"})})]})]}),"cash"===n&&(0,t.jsxs)("div",{className:i().cashPaymentInterface,children:[t.jsx("h4",{children:"Cash Payment Details"}),(0,t.jsxs)("div",{className:i().cashInputs,children:[t.jsx("div",{className:i().inputGroup,children:(0,t.jsxs)("label",{children:["Amount Due: ",N(u)]})}),(0,t.jsxs)("div",{className:i().inputGroup,children:[t.jsx("label",{children:"Cash Received:"}),t.jsx("input",{type:"number",step:"0.01",min:u,value:c,onChange:e=>o(e.target.value),placeholder:u.toFixed(2),className:i().cashInput})]}),c&&parseFloat(c)>=u&&t.jsx("div",{className:i().changeAmount,children:(0,t.jsxs)("strong",{children:["Change: ",N(parseFloat(c)-u)]})})]}),t.jsx("div",{className:i().cashActions,children:t.jsx("button",{onClick:()=>{let e=parseFloat(c);if(!e||e<u){alert(`Please enter at least $${u.toFixed(2)}`);return}let a=e-u;m(!0),s("cash",{originalAmount:u,processingFee:0,totalAmount:u,cashReceived:e,changeAmount:a})},disabled:!c||parseFloat(c)<u,className:i().processCashButton,children:"Process Cash Payment"})})]}),t.jsx("div",{className:i().paymentActions,children:t.jsx("button",{onClick:a,className:i().cancelButton,children:"Cancel"})}),t.jsx("div",{className:i().paymentNote,children:t.jsx("p",{children:"\uD83D\uDCA1 Processing fees are automatically calculated and included in the total"})})]})}function c({amount:e,currency:s="AUD",onSuccess:a,onError:n,orderDetails:l={}}){let[c,o]=(0,r.useState)(null),[d,m]=(0,r.useState)(!0),[u,h]=(0,r.useState)(!1),[p,x]=(0,r.useState)(""),v=(0,r.useRef)(!1),[j,N]=(0,r.useState)({addressLine1:"1455 Market St",addressLine2:"Suite 600",locality:"San Francisco",administrativeDistrictLevel1:"CA",postalCode:"94103",country:"US"}),[g,y]=(0,r.useState)(!1),S=(0,r.useRef)(null),_=(0,r.useRef)(!1),C=(0,r.useRef)(null),f=(0,r.useRef)(!1);(0,r.useRef)(null),(0,r.useRef)(null);let P=(0,r.useCallback)(async()=>{performance.now();try{if(f.current=!0,!_.current){console.warn("InitializeSquareForm: Component unmounted before starting."),f.current=!1;return}if(v.current){console.log("InitializeSquareForm: Already attempted in this lifecycle."),f.current=!1;return}if(!S.current){console.error("InitializeSquareForm: Container ref not available"),f.current=!1;return}throw x(""),Error("Square SDK not loaded")}catch(e){console.error("Square form initialization error:",e),x(e.message||"Failed to initialize payment form"),m(!1)}finally{f.current=!1}},[]),b=()=>{v.current=!1,x(""),m(!0),P()},D=(0,r.useCallback)(async()=>{if(c&&!u){h(!0),x("");try{try{let{startPOSPaymentOperation:e}=await Promise.resolve().then(function(){var e=Error("Cannot find module '@/lib/pos-auth-protection'");throw e.code="MODULE_NOT_FOUND",e});e()}catch(e){console.warn("POS payment protection not available:",e)}console.log("\uD83D\uDD04 Tokenizing card...");let e=g?{billingContact:{addressLines:[j.addressLine1,j.addressLine2].filter(Boolean),city:j.locality,countryCode:j.country,postalCode:j.postalCode,state:j.administrativeDistrictLevel1}}:{},s=await c.tokenize(e);if("OK"===s.status){console.log("✅ Card tokenized successfully");let e=await k(s.token);a(e)}else{console.error("❌ Tokenization failed:",s.errors);let e=s.errors?.[0]?.message||"Card tokenization failed";x(e),n(Error(e))}}catch(e){console.error("Payment processing error:",e),x(e.message||"Payment failed. Please try again."),n(e)}finally{h(!1);try{let{endPOSPaymentOperation:e}=await Promise.resolve().then(function(){var e=Error("Cannot find module '@/lib/pos-auth-protection'");throw e.code="MODULE_NOT_FOUND",e});e()}catch(e){console.warn("Error ending POS payment protection:",e)}}}},[u,g,j,e,s,a,n]),k=async a=>{console.log("\uD83D\uDD04 Processing payment with token...");try{let t=await fetch("/api/admin/pos/process-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sourceId:a,amount:Math.round(100*parseFloat(e)),currency:s,orderDetails:l,idempotencyKey:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`})});if(!t.ok){let e=await t.json();throw Error(e.message||`Payment failed: ${t.status}`)}let r=await t.json();return console.log("✅ Payment processed successfully:",r),r}catch(e){throw console.error("Payment API error:",e),e}};return console.log("\uD83D\uDD0D POSSquarePayment render state:",{showBillingAddress:g,isLoading:d,paymentForm:!!C.current,squareSDKLoaded:!1,initializationAttempted:v.current}),(0,t.jsxs)("div",{className:i().squarePaymentContainer,children:[(0,t.jsxs)("div",{className:i().paymentFormHeader,children:[t.jsx("h4",{children:"Card Payment"}),(0,t.jsxs)("div",{className:i().paymentAmount,children:["Amount: ",(0,t.jsxs)("span",{children:["$",parseFloat(e||0).toFixed(2)," ",s]})]})]}),p&&(0,t.jsxs)("div",{className:i().paymentError,children:[t.jsx("span",{className:i().errorIcon,children:"⚠️"}),(0,t.jsxs)("div",{className:i().errorContent,children:[t.jsx("div",{className:i().errorText,children:p}),t.jsx("button",{onClick:b,className:i().retryButton,children:"Retry"})]})]}),(0,t.jsxs)("div",{className:i().cardFormContainer,children:[t.jsx("div",{ref:S,className:i().cardForm,style:{minHeight:"60px",border:"1px solid #e0e0e0",borderRadius:"8px",padding:"16px",background:"white"},children:d&&(0,t.jsxs)("div",{className:i().cardFormPlaceholder,children:[t.jsx("div",{className:i().loadingSpinner}),t.jsx("p",{children:"Initializing secure payment form..."})]})}),g&&(0,t.jsxs)("div",{className:i().billingAddressSection,children:[t.jsx("h5",{children:"Billing Address"}),(0,t.jsxs)("div",{className:i().addressGrid,children:[(0,t.jsxs)("div",{className:i().addressField,children:[t.jsx("label",{children:"Address Line 1"}),t.jsx("input",{type:"text",value:j.addressLine1,onChange:e=>N(s=>({...s,addressLine1:e.target.value})),placeholder:"1455 Market St"})]}),(0,t.jsxs)("div",{className:i().addressField,children:[t.jsx("label",{children:"Address Line 2"}),t.jsx("input",{type:"text",value:j.addressLine2,onChange:e=>N(s=>({...s,addressLine2:e.target.value})),placeholder:"Suite 600"})]}),(0,t.jsxs)("div",{className:i().addressField,children:[t.jsx("label",{children:"City"}),t.jsx("input",{type:"text",value:j.locality,onChange:e=>N(s=>({...s,locality:e.target.value})),placeholder:"San Francisco"})]}),(0,t.jsxs)("div",{className:i().addressField,children:[t.jsx("label",{children:"State"}),t.jsx("input",{type:"text",value:j.administrativeDistrictLevel1,onChange:e=>N(s=>({...s,administrativeDistrictLevel1:e.target.value})),placeholder:"CA"})]}),(0,t.jsxs)("div",{className:i().addressField,children:[t.jsx("label",{children:"ZIP Code"}),t.jsx("input",{type:"text",value:j.postalCode,onChange:e=>N(s=>({...s,postalCode:e.target.value})),placeholder:"94103"})]}),(0,t.jsxs)("div",{className:i().addressField,children:[t.jsx("label",{children:"Country"}),(0,t.jsxs)("select",{value:j.country,onChange:e=>N(s=>({...s,country:e.target.value})),children:[t.jsx("option",{value:"US",children:"United States"}),t.jsx("option",{value:"AU",children:"Australia"}),t.jsx("option",{value:"CA",children:"Canada"}),t.jsx("option",{value:"GB",children:"United Kingdom"})]})]})]}),t.jsx("div",{className:i().addressNote,children:t.jsx("small",{children:"\uD83D\uDCA1 Billing address is required for card verification in sandbox mode"})})]})]}),(0,t.jsxs)("div",{className:i().paymentActions,children:[t.jsx("button",{onClick:D,disabled:!c||u||d,className:i().payButton,children:u?(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:i().buttonSpinner}),"Processing..."]}):`Pay ${s} $${parseFloat(e||0).toFixed(2)}`}),!c&&!d&&!p&&(0,t.jsxs)("div",{className:i().formNotReady,children:[t.jsx("span",{className:i().errorIcon,children:"⚠️"}),"Card form not initialized. Please wait for the form to load."]}),p&&(0,t.jsxs)("div",{className:i().errorContainer,children:[t.jsx("p",{className:i().errorMessage,children:p}),t.jsx("button",{onClick:b,className:i().retryButton,children:"Retry"})]})]}),(0,t.jsxs)("div",{className:i().paymentSecurity,children:[(0,t.jsxs)("div",{className:i().securityBadges,children:[t.jsx("span",{className:i().securityBadge,children:"\uD83D\uDD12 SSL Encrypted"}),t.jsx("span",{className:i().securityBadge,children:"✅ PCI Compliant"}),t.jsx("span",{className:i().securityBadge,children:"\uD83D\uDEE1️ Square Secure"})]}),t.jsx("p",{className:i().securityText,children:"Your payment information is processed securely by Square and never stored on our servers."})]})]})}function o({amount:e,currency:s="AUD",orderDetails:a,onSuccess:n,onError:l,onCancel:c}){let[o,d]=(0,r.useState)([]),[m,u]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),[x,v]=(0,r.useState)(null),[j,N]=(0,r.useState)(null),[g,y]=(0,r.useState)(""),[S,_]=(0,r.useState)(!0),C=async()=>{try{_(!0);let e=await fetch("/api/admin/pos/terminal-devices",{headers:{Authorization:`Bearer ${localStorage.getItem("adminToken")}`}});if(e.ok){let s=await e.json();d(s.devices||[])}else console.error("Failed to load terminal devices"),d([])}catch(e){console.error("Error loading terminal devices:",e),d([])}finally{_(!1)}},f=async()=>{if(!m){y("Please select a terminal device");return}try{p(!0),y("");let t=await fetch("/api/admin/pos/terminal-checkout",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("adminToken")}`},body:JSON.stringify({deviceId:m.id,amountMoney:{amount:Math.round(100*parseFloat(e)),currency:s},note:a?.service||"POS Payment",orderId:a?.orderId||`pos_${Date.now()}`,paymentOptions:{autocomplete:!0,collectSignature:!0,allowTipping:!1}})});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to create terminal checkout")}let r=await t.json();v(r.checkout.id),N(r.checkout.status),console.log("Terminal checkout created:",r.checkout)}catch(e){console.error("Error creating terminal checkout:",e),y(e.message),p(!1),l(e)}};return S?t.jsx("div",{className:i().terminalPaymentContainer,children:(0,t.jsxs)("div",{className:i().loadingState,children:[t.jsx("div",{className:i().loadingSpinner}),t.jsx("p",{children:"Loading terminal devices..."})]})}):0===o.length?t.jsx("div",{className:i().terminalPaymentContainer,children:(0,t.jsxs)("div",{className:i().noDevicesState,children:[t.jsx("div",{className:i().noDevicesIcon,children:"\uD83D\uDCF1"}),t.jsx("h3",{children:"No Terminal Devices Available"}),t.jsx("p",{children:"No paired Square Terminal devices found. Please pair a device first."}),t.jsx("button",{className:i().refreshButton,onClick:C,children:"Refresh Devices"})]})}):(0,t.jsxs)("div",{className:i().terminalPaymentContainer,children:[(0,t.jsxs)("div",{className:i().terminalHeader,children:[t.jsx("h3",{children:"Square Terminal Payment"}),(0,t.jsxs)("div",{className:i().paymentAmount,children:["$",parseFloat(e||0).toFixed(2)," ",s]})]}),!h&&(0,t.jsxs)("div",{className:i().deviceSelection,children:[t.jsx("h4",{children:"Select Terminal Device"}),t.jsx("div",{className:i().deviceList,children:o.map(e=>(0,t.jsxs)("div",{className:`${i().deviceCard} ${m?.id===e.id?i().selected:""}`,onClick:()=>u(e),children:[t.jsx("div",{className:i().deviceIcon,children:"\uD83D\uDCF1"}),(0,t.jsxs)("div",{className:i().deviceInfo,children:[t.jsx("div",{className:i().deviceName,children:e.name||`Terminal ${e.id.slice(-4)}`}),(0,t.jsxs)("div",{className:i().deviceStatus,children:["\uD83D\uDFE2 ",e.status]})]})]},e.id))})]}),h&&(0,t.jsxs)("div",{className:i().processingState,children:[t.jsx("div",{className:i().statusIcon,children:(()=>{switch(j){case"PENDING":case"IN_PROGRESS":return"⏳";case"COMPLETED":return"✅";case"CANCELED":case"ERROR":return"❌";default:return"\uD83D\uDCF1"}})()}),t.jsx("div",{className:i().statusMessage,children:(()=>{switch(j){case"PENDING":return"Waiting for customer to complete payment on terminal...";case"IN_PROGRESS":return"Payment in progress on terminal...";case"COMPLETED":return"Payment completed successfully!";case"CANCELED":return"Payment was cancelled";case"ERROR":return"Payment failed";default:return"Preparing terminal checkout..."}})()}),"PENDING"===j&&(0,t.jsxs)("div",{className:i().terminalInstructions,children:[t.jsx("p",{children:"Customer should now see the payment screen on the terminal device."}),t.jsx("p",{children:"They can insert their card, tap for contactless, or use mobile payment."})]})]}),g&&(0,t.jsxs)("div",{className:i().errorMessage,children:[t.jsx("div",{className:i().errorIcon,children:"⚠️"}),t.jsx("div",{className:i().errorText,children:g})]}),t.jsx("div",{className:i().terminalActions,children:h?t.jsx("button",{className:i().cancelButton,onClick:()=>{p(!1),v(null),N(null),c()},children:"Cancel Payment"}):t.jsx("button",{className:i().startPaymentButton,onClick:f,disabled:!m,children:"Start Terminal Payment"})})]})}function d({service:e,artist:s,tier:n,timeSlot:d,onBack:u,onComplete:h}){let[p,x]=(0,r.useState)("customer"),[v,j]=(0,r.useState)(null),[N,g]=(0,r.useState)(null),[y,S]=(0,r.useState)(null),[_,C]=(0,r.useState)(!1),[f,P]=(0,r.useState)(null),b=n?.price||0,D=async e=>{try{C(!0),P(null);let s=await w("cash",{cashReceived:e?.cashReceived,changeAmount:e?.changeAmount,totalAmount:e?.totalAmount||b});if(s.success)h(s);else throw Error(s.error||"Failed to process cash payment")}catch(e){console.error("Cash payment error:",e),P(e.message)}finally{C(!1)}},k=async e=>{try{C(!0),P(null);let s=e.paymentDetails?.deviceId?"square_terminal":"card",a=await w(s,e);if(a.success)h(a);else throw Error(a.error||"Failed to record payment")}catch(e){console.error("Payment completion error:",e),P(e.message)}finally{C(!1)}},O=e=>{console.error("Square payment error:",e),P(e.message||"Card payment failed")},w=async(t,r=null)=>{try{console.log("\uD83D\uDD04 Creating POS booking...");let{supabase:i}=await a.e(732).then(a.bind(a,2732)),{data:{session:l},error:c}=await i.auth.getSession();if(console.log("Booking authentication check:",{hasSession:!!l,hasUser:!!l?.user,hasToken:!!l?.access_token,userEmail:l?.user?.email,error:c?.message}),!l?.access_token)throw Error("Authentication required. Please refresh the page and try again.");let o={customer_name:v?.name||"Walk-in Customer",customer_email:v?.email||"",customer_phone:v?.phone||"",service_id:e.id,service_name:e.name,artist_id:s.id,artist_name:s.name,tier_id:n.id,tier_name:n.name,duration:n.duration,start_time:d.time,end_time:new Date(new Date(d.time).getTime()+6e4*n.duration).toISOString(),total_amount:y?.totalAmount||b,status:"completed",payment_method:t,payment_status:"completed",notes:`POS Booking - ${s.name} - ${n.name}`,booking_type:"pos",created_via:"admin_pos"};r&&("cash"===t?(o.cash_received=r.cashReceived,o.change_amount=r.changeAmount):(o.payment_transaction_id=r.paymentId||r.transactionId,o.processing_fee=y?.processingFee||0)),console.log("Creating booking with data:",o);let m=await fetch("/api/admin/pos/create-booking",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${l.access_token}`},body:JSON.stringify(o)});if(!m.ok){let e=await m.text();throw console.error("Booking creation failed:",m.status,e),Error(`Failed to create booking: ${m.status}`)}let u=await m.json();return console.log("✅ Booking created successfully:",u),{success:!0,booking:u.booking,payment:u.payment,message:"Booking and payment processed successfully!"}}catch(e){return console.error("Error creating booking:",e),{success:!1,error:e.message}}};return(0,t.jsxs)("div",{className:i().posCheckoutContainer,children:[(0,t.jsxs)("div",{className:i().checkoutHeader,children:[t.jsx("button",{onClick:u,className:i().backButton,children:"← Back"}),t.jsx("h2",{children:"Complete Booking"})]}),(0,t.jsxs)("div",{className:i().bookingSummary,children:[t.jsx("h3",{children:"Booking Details"}),(0,t.jsxs)("div",{className:i().summaryGrid,children:[(0,t.jsxs)("div",{className:i().summaryItem,children:[t.jsx("strong",{children:"Service:"})," ",e.name]}),(0,t.jsxs)("div",{className:i().summaryItem,children:[t.jsx("strong",{children:"Artist:"})," ",s.name]}),(0,t.jsxs)("div",{className:i().summaryItem,children:[t.jsx("strong",{children:"Duration:"})," ",(e=>{if(!e)return"";let s=Math.floor(e/60),a=e%60;return s>0?a>0?`${s}h ${a}m`:`${s}h`:`${a}m`})(n.duration)]}),(0,t.jsxs)("div",{className:i().summaryItem,children:[t.jsx("strong",{children:"Price:"})," $",n.price?.toFixed(2)]}),(0,t.jsxs)("div",{className:i().summaryItem,children:[t.jsx("strong",{children:"Date & Time:"})," ",new Date(d.time).toLocaleString()]})]})]}),f&&(0,t.jsxs)("div",{className:i().errorAlert,children:[t.jsx("span",{className:i().errorIcon,children:"⚠️"}),t.jsx("div",{className:i().errorText,children:f}),t.jsx("button",{onClick:()=>P(null),className:i().closeError,children:"\xd7"})]}),"customer"===p&&(0,t.jsxs)("div",{className:i().customerStep,children:[t.jsx("h3",{children:"Customer Information"}),t.jsx("div",{className:i().customerForm,children:t.jsx(m,{onSubmit:e=>{j(e),x("payment")}})})]}),"payment"===p&&!N&&t.jsx(l,{amount:b,onMethodSelect:(e,s)=>{g(e),S(s),"cash"===e&&D(s)},onCancel:()=>x("customer")}),"payment"===p&&"square_payment"===N&&t.jsx(c,{amount:y?.totalAmount||b,currency:"AUD",onSuccess:k,onError:O,orderDetails:{service:e.name,tier:n.name,customer:v?.name||"Walk-in Customer",orderId:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`}}),"payment"===p&&"square_terminal"===N&&t.jsx(o,{amount:y?.totalAmount||b,currency:"AUD",onSuccess:k,onError:O,onCancel:()=>g(null),orderDetails:{service:e.name,tier:n.name,customer:v?.name||"Walk-in Customer",orderId:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`}}),_&&t.jsx("div",{className:i().processingOverlay,children:(0,t.jsxs)("div",{className:i().processingContent,children:[t.jsx("div",{className:i().loadingSpinner}),t.jsx("p",{children:"Processing booking and payment..."})]})})]})}function m({onSubmit:e}){let[s,a]=(0,r.useState)({name:"",email:"",phone:"",notes:""}),[n,l]=(0,r.useState)(!1),c=(e,s)=>{a(a=>({...a,[e]:s}))};return(0,t.jsxs)("form",{onSubmit:a=>{if(a.preventDefault(),n)e({name:"Walk-in Customer",email:"",phone:"",notes:s.notes});else{if(!s.name.trim()){alert("Please enter customer name");return}e(s)}},className:i().customerForm,children:[t.jsx("div",{className:i().walkInToggle,children:(0,t.jsxs)("label",{children:[t.jsx("input",{type:"checkbox",checked:n,onChange:e=>l(e.target.checked)}),"Walk-in Customer (no details required)"]})}),!n&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:i().formGroup,children:[t.jsx("label",{children:"Customer Name *"}),t.jsx("input",{type:"text",value:s.name,onChange:e=>c("name",e.target.value),placeholder:"Enter customer name",required:!0})]}),(0,t.jsxs)("div",{className:i().formGroup,children:[t.jsx("label",{children:"Email"}),t.jsx("input",{type:"email",value:s.email,onChange:e=>c("email",e.target.value),placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:i().formGroup,children:[t.jsx("label",{children:"Phone"}),t.jsx("input",{type:"tel",value:s.phone,onChange:e=>c("phone",e.target.value),placeholder:"+61 XXX XXX XXX"})]})]}),(0,t.jsxs)("div",{className:i().formGroup,children:[t.jsx("label",{children:"Special Notes"}),t.jsx("textarea",{value:s.notes,onChange:e=>c("notes",e.target.value),placeholder:"Any special requirements or notes...",rows:3})]}),t.jsx("button",{type:"submit",className:i().continueButton,children:"Continue to Payment"})]})}},660:(e,s,a)=>{"use strict";a.d(s,{Z:()=>l});var t=a(997),r=a(6689);!function(){var e=Error("Cannot find module '@/lib/safe-render-utils'");throw e.code="MODULE_NOT_FOUND",e}();var n=a(1073),i=a.n(n);function l({service:e,onBookingSlotSelect:s,onBack:a}){let[n,l]=(0,r.useState)(new Date().toISOString().split("T")[0]),[c,o]=(0,r.useState)(null),[d,m]=(0,r.useState)(null),[u,h]=(0,r.useState)([]),[p,x]=(0,r.useState)(!1),[v,j]=(0,r.useState)(null),N=e?.availableArtists?.filter(e=>e.isAvailableToday)||[],g=e?.pricing_tiers||[],y=e=>{o(e)},S=e=>{m(e)},_=e=>{"available"===e.status&&c&&d&&s(c,d,{...e,time:e.time})},C=e=>new Date(e).toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit",hour12:!1}),f=e=>`$${parseFloat(e||0).toFixed(2)}`,P=c&&d;return(0,t.jsxs)("div",{className:i().serviceBookingAvailability,children:[(0,t.jsxs)("div",{className:i().bookingHeader,children:[t.jsx("button",{className:i().backButton,onClick:a,children:"← Back to Services"}),(0,t.jsxs)("div",{className:i().serviceInfo,children:[t.jsx("h2",{className:i().serviceName,children:Object(function(){var e=Error("Cannot find module '@/lib/safe-render-utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.name,"Service")}),t.jsx("p",{className:i().serviceDescription,children:Object(function(){var e=Error("Cannot find module '@/lib/safe-render-utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.description,"")})]})]}),(0,t.jsxs)("div",{className:i().bookingSelectionGrid,children:[(0,t.jsxs)("div",{className:i().selectionSection,children:[t.jsx("h3",{className:i().sectionTitle,children:"Choose Artist"}),0===N.length?t.jsx("div",{className:i().noOptions,children:t.jsx("p",{children:"No artists available for this service today"})}):t.jsx("div",{className:i().artistGrid,children:N.map(e=>(0,t.jsxs)("div",{className:`${i().artistCard} ${c?.id===e.id?i().selected:""}`,onClick:()=>y(e),children:[t.jsx("div",{className:i().artistAvatar,children:e.name?.charAt(0)?.toUpperCase()||"?"}),(0,t.jsxs)("div",{className:i().artistInfo,children:[t.jsx("h4",{className:i().artistName,children:Object(function(){var e=Error("Cannot find module '@/lib/safe-render-utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.name,"Unknown Artist")}),t.jsx("p",{className:i().artistRole,children:Object(function(){var e=Error("Cannot find module '@/lib/safe-render-utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.role||e.specialties?.[0],"Artist")})]})]},e.id))})]}),(0,t.jsxs)("div",{className:i().selectionSection,children:[t.jsx("h3",{className:i().sectionTitle,children:"Choose Duration & Price"}),0===g.length?t.jsx("div",{className:i().noOptions,children:t.jsx("p",{children:"No pricing options available"})}):t.jsx("div",{className:i().tierGrid,children:g.map(e=>(0,t.jsxs)("div",{className:`${i().tierCard} ${d?.id===e.id?i().selected:""}`,onClick:()=>S(e),children:[t.jsx("h4",{className:i().tierName,children:Object(function(){var e=Error("Cannot find module '@/lib/safe-render-utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.name,"Standard")}),t.jsx("div",{className:i().tierPrice,children:f(e.price)}),(0,t.jsxs)("div",{className:i().tierDuration,children:[Object(function(){var e=Error("Cannot find module '@/lib/safe-render-utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.duration,"30")," minutes"]}),e.description&&t.jsx("p",{className:i().tierDescription,children:e.description})]},e.id))})]}),(0,t.jsxs)("div",{className:i().selectionSection,children:[t.jsx("h3",{className:i().sectionTitle,children:"Choose Time Slot"}),(0,t.jsxs)("div",{className:i().dateSelector,children:[t.jsx("label",{htmlFor:"booking-date-select",children:"Date:"}),t.jsx("input",{id:"booking-date-select",type:"date",value:n,onChange:e=>{l(e.target.value)},className:i().dateInput,min:new Date().toISOString().split("T")[0]})]}),P?p?(0,t.jsxs)("div",{className:i().loading,children:[t.jsx("div",{className:i().loadingSpinner}),t.jsx("p",{children:"Loading time slots..."})]}):v?t.jsx("div",{className:`${i().errorNotice} p-3 bg-red-100 border border-red-400 text-red-700 rounded-md`,children:(0,t.jsxs)("p",{children:["Error: ",v]})}):u.length>0?t.jsx("div",{className:i().timeSlots,children:u.map(e=>t.jsx("div",{className:`${i().timeSlot} ${i()[e.status]||("available"===e.status?i().available:i().unavailable)} ${"available"===e.status?i().clickable:""}`,onClick:()=>_(e),title:`${C(e.time)} - ${e.status}`,children:C(e.time)},e.id||e.time))}):t.jsx("div",{className:i().noOptions,children:t.jsx("p",{children:"No available slots for this day, artist, or service duration. Try changing the date or service options."})}):t.jsx("div",{className:i().selectPrompt,children:t.jsx("p",{children:"Please select an artist and duration first"})})]})]}),(c||d)&&(0,t.jsxs)("div",{className:i().selectionSummary,children:[t.jsx("h4",{children:"Current Selection"}),(0,t.jsxs)("div",{className:i().summaryGrid,children:[c&&(0,t.jsxs)("div",{className:i().summaryItem,children:[t.jsx("strong",{children:"Artist:"})," ",c.name]}),d&&(0,t.jsxs)("div",{className:i().summaryItem,children:[t.jsx("strong",{children:"Duration:"})," ",d.duration," minutes"]}),d&&(0,t.jsxs)("div",{className:i().summaryItem,children:[t.jsx("strong",{children:"Price:"})," ",f(d.price)]})]}),P&&t.jsx("p",{className:i().nextStep,children:"Now select an available time slot to continue"})]})]})}},4679:(e,s,a)=>{"use strict";a.a(e,async(e,t)=>{try{a.r(s),a.d(s,{default:()=>x});var r=a(997),n=a(6689),i=a(968),l=a.n(i),c=a(8568),o=a(4845),d=a(660),m=a(2865),u=a(1073),h=a.n(u),p=e([o]);function x(){let{user:e,loading:s}=(0,c.a)(),[a,t]=(0,n.useState)(!0),[i,u]=(0,n.useState)([]),[p,x]=(0,n.useState)([]),[v,j]=(0,n.useState)("services"),[N,g]=(0,n.useState)(null),[y,S]=(0,n.useState)(null),[_,C]=(0,n.useState)([]),[f,P]=(0,n.useState)(null),[b,D]=(0,n.useState)(0),[k,O]=(0,n.useState)("card"),[w,I]=(0,n.useState)(!1),[A,B]=(0,n.useState)(""),[F,E]=(0,n.useState)(""),T=e=>{g(e),j("booking")},q=e=>{_.find(s=>s.id===e.id)?C(_.map(s=>s.id===e.id?{...s,quantity:s.quantity+1}:s)):C([..._,{id:e.id,name:e.name,price:e.price||0,quantity:1,duration:e.duration}])},$=e=>{C(_.filter(s=>s.id!==e))},L=(e,s)=>{if(s<=0){$(e);return}C(_.map(a=>a.id===e?{...a,quantity:s}:a))},M=()=>{C([]),P(null),D(0)},U=async()=>{if(0!==_.length){I(!0);try{let e=localStorage.getItem("admin-token"),s={customer_id:f?.id,customer_name:f?.name||"Walk-in Customer",customer_email:f?.email||"",customer_phone:f?.phone||"",services:_.map(e=>({service_id:e.id,service_name:e.name,quantity:e.quantity,price:e.price,duration:e.duration})),total_amount:b,payment_method:k,status:"completed",booking_date:new Date().toISOString(),notes:"POS Transaction"};if((await fetch("/api/admin/bookings",{method:"POST",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify(s)})).ok)alert("Transaction completed successfully!"),M();else throw Error("Failed to process transaction")}catch(e){console.error("Error processing transaction:",e),alert("Error processing transaction. Please try again.")}finally{I(!1)}}},R=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),G=e=>{if(!e)return"";if(e>=60){let s=Math.floor(e/60),a=e%60;return a>0?`${s}h ${a}m`:`${s}h`}return`${e}m`},z=i.filter(e=>e.name.toLowerCase().includes(A.toLowerCase())||e.category.toLowerCase().includes(A.toLowerCase()));return(p.filter(e=>e.name.toLowerCase().includes(F.toLowerCase())||e.email.toLowerCase().includes(F.toLowerCase())||e.phone&&e.phone.includes(F)),s||a)?r.jsx(o.Z,{children:(0,r.jsxs)("div",{className:h().loadingContainer,children:[r.jsx("div",{className:h().loadingSpinner}),r.jsx("p",{children:"Loading POS system..."})]})}):e?(0,r.jsxs)(o.Z,{children:[(0,r.jsxs)(l(),{children:[r.jsx("title",{children:"Point of Sale | Ocean Soul Sparkles Admin"}),r.jsx("meta",{name:"description",content:"Process transactions and manage sales"})]}),(0,r.jsxs)("div",{className:h().posContainer,children:["services"===v&&(0,r.jsxs)("div",{className:h().posContent,children:[(0,r.jsxs)("div",{className:h().servicesSection,children:[(0,r.jsxs)("div",{className:h().sectionHeader,children:[r.jsx("h2",{children:"Services"}),r.jsx("p",{children:"Select a service to start booking with calendar and payment processing"}),r.jsx("input",{type:"text",placeholder:"Search services...",value:A,onChange:e=>B(e.target.value),className:h().searchInput})]}),r.jsx("div",{className:h().servicesGrid,children:z.map(e=>(0,r.jsxs)("div",{className:h().serviceCard,children:[r.jsx("h3",{className:h().serviceName,children:e.name}),r.jsx("p",{className:h().serviceCategory,children:e.category}),(0,r.jsxs)("div",{className:h().serviceDetails,children:[r.jsx("span",{className:h().price,children:e.price?R(e.price):"From $50"}),e.duration&&r.jsx("span",{className:h().duration,children:G(e.duration)})]}),(0,r.jsxs)("div",{className:h().serviceActions,children:[r.jsx("button",{onClick:()=>T(e),className:h().bookServiceBtn,children:"\uD83D\uDCC5 Book with Calendar"}),r.jsx("button",{onClick:()=>q(e),className:h().addToCartBtn,disabled:!e.price,children:"\uD83D\uDED2 Quick Add"})]})]},e.id))})]}),_.length>0&&(0,r.jsxs)("div",{className:h().quickCart,children:[(0,r.jsxs)("h3",{children:["Quick Cart (",_.length," items)"]}),r.jsx("div",{className:h().cartItems,children:_.map(e=>(0,r.jsxs)("div",{className:h().cartItem,children:[(0,r.jsxs)("div",{className:h().itemInfo,children:[r.jsx("span",{className:h().itemName,children:e.name}),r.jsx("span",{className:h().itemPrice,children:R(e.price)})]}),(0,r.jsxs)("div",{className:h().quantityControls,children:[r.jsx("button",{onClick:()=>L(e.id,e.quantity-1),className:h().quantityBtn,children:"-"}),r.jsx("span",{className:h().quantity,children:e.quantity}),r.jsx("button",{onClick:()=>L(e.id,e.quantity+1),className:h().quantityBtn,children:"+"})]}),r.jsx("span",{className:h().itemTotal,children:R(e.price*e.quantity)}),r.jsx("button",{onClick:()=>$(e.id),className:h().removeBtn,children:"\xd7"})]},e.id))}),(0,r.jsxs)("div",{className:h().cartTotal,children:["Total: ",R(b)]}),(0,r.jsxs)("div",{className:h().cartActions,children:[r.jsx("button",{onClick:M,className:h().clearBtn,children:"Clear"}),r.jsx("button",{onClick:U,className:h().quickCheckoutBtn,disabled:w,children:w?"Processing...":"Quick Checkout"})]})]})]}),"booking"===v&&N&&r.jsx(d.Z,{service:N,onBookingSlotSelect:(e,s,a)=>{S({artist:e,tier:s,timeSlot:a}),j("checkout")},onBack:()=>{j("services"),g(null),S(null)}}),"checkout"===v&&y&&r.jsx(m.Z,{service:N,artist:y.artist,tier:y.tier,timeSlot:y.timeSlot,onBack:()=>{j("booking"),S(null)},onComplete:e=>{console.log("Checkout completed:",e),j("services"),g(null),S(null),alert("Booking completed successfully!")}})]})]}):null}o=(p.then?(await p)():p)[0],t()}catch(e){t(e)}})},2885:e=>{"use strict";e.exports=require("@supabase/supabase-js")},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[899,212,664,441],()=>a(519));module.exports=t})();