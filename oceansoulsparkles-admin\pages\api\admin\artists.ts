import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify admin authentication
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    if (req.method === 'GET') {
      // Try to get artists from users table with artist role, or create mock data structure
      const { data: artists, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'artist');

      if (error && error.message.includes('relation "users" does not exist')) {
        // If no users table, return structured mock data for now
        const mockArtists = [
          {
            id: 1,
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '0412 345 678',
            specializations: ['Box Braids', 'Cornrows', 'Twists'],
            status: 'active',
            availability: {
              monday: { start: '09:00', end: '17:00', available: true },
              tuesday: { start: '09:00', end: '17:00', available: true },
              wednesday: { start: '09:00', end: '17:00', available: true },
              thursday: { start: '09:00', end: '17:00', available: true },
              friday: { start: '09:00', end: '17:00', available: true },
              saturday: { start: '10:00', end: '16:00', available: true },
              sunday: { start: '10:00', end: '16:00', available: false }
            },
            rating: 4.8,
            total_bookings: 127,
            total_revenue: 15240,
            created_at: '2024-01-15T00:00:00Z'
          },
          {
            id: 2,
            name: 'Maya Patel',
            email: '<EMAIL>',
            phone: '0423 456 789',
            specializations: ['Locs', 'Faux Locs', 'Goddess Braids'],
            status: 'active',
            availability: {
              monday: { start: '10:00', end: '18:00', available: true },
              tuesday: { start: '10:00', end: '18:00', available: true },
              wednesday: { start: '10:00', end: '18:00', available: false },
              thursday: { start: '10:00', end: '18:00', available: true },
              friday: { start: '10:00', end: '18:00', available: true },
              saturday: { start: '09:00', end: '17:00', available: true },
              sunday: { start: '11:00', end: '15:00', available: true }
            },
            rating: 4.9,
            total_bookings: 98,
            total_revenue: 12800,
            created_at: '2024-02-01T00:00:00Z'
          },
          {
            id: 3,
            name: 'Keisha Williams',
            email: '<EMAIL>',
            phone: '0434 567 890',
            specializations: ['Senegalese Twists', 'Marley Twists', 'Passion Twists'],
            status: 'active',
            availability: {
              monday: { start: '08:00', end: '16:00', available: true },
              tuesday: { start: '08:00', end: '16:00', available: true },
              wednesday: { start: '08:00', end: '16:00', available: true },
              thursday: { start: '08:00', end: '16:00', available: true },
              friday: { start: '08:00', end: '16:00', available: true },
              saturday: { start: '09:00', end: '15:00', available: true },
              sunday: { start: '09:00', end: '15:00', available: false }
            },
            rating: 4.7,
            total_bookings: 145,
            total_revenue: 18600,
            created_at: '2024-01-20T00:00:00Z'
          }
        ];
        
        return res.status(200).json({ artists: mockArtists, source: 'mock' });
      }

      if (error) {
        throw error;
      }

      return res.status(200).json({ artists: artists || [], source: 'database' });
    }

    if (req.method === 'POST') {
      const { name, email, phone, specializations, availability } = req.body;

      const { data, error } = await supabase
        .from('users')
        .insert([
          {
            name,
            email,
            phone,
            role: 'artist',
            specializations,
            availability,
            status: 'active',
            created_at: new Date().toISOString()
          }
        ])
        .select();

      if (error) {
        throw error;
      }

      return res.status(201).json({ artist: data[0] });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Artists API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
