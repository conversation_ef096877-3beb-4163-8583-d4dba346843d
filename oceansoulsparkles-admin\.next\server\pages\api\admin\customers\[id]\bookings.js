"use strict";(()=>{var e={};e.id=741,e.ids=[741],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},544:(e,t,r)=>{r.r(t),r.d(t,{config:()=>_,default:()=>m,routeModule:()=>p});var s={};r.r(s),r.d(s,{default:()=>c});var a=r(1802),i=r(7153),o=r(8781),n=r(7474),d=r(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,d.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function c(e,t){let{id:r}=e.query;if(!r||"string"!=typeof r)return t.status(400).json({error:"Customer ID is required"});if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let s=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!s)return t.status(401).json({error:"No authentication token"});let a=await (0,n.Wg)(s);if(!a.valid||!a.user)return t.status(401).json({error:"Invalid authentication"});let{data:i,error:o}=await l.from("bookings").select(`
        id,
        booking_date,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        created_at,
        services (
          id,
          name,
          duration,
          price
        ),
        artist_profiles!assigned_artist_id (
          id,
          artist_name,
          display_name
        )
      `).eq("customer_id",r).order("booking_date",{ascending:!1}).order("start_time",{ascending:!1});if(o)return console.error("Customer bookings query error:",o),t.status(500).json({error:"Failed to fetch customer bookings"});let d=(i||[]).map(e=>{let t=Array.isArray(e.services)?e.services[0]:e.services,r=Array.isArray(e.artist_profiles)?e.artist_profiles[0]:e.artist_profiles,s=new Date(e.start_time).toTimeString().slice(0,5);return{id:e.id,service_name:t?.name||"Unknown Service",service_duration:t?.duration||0,service_price:t?.price||0,artist_name:r?.artist_name||r?.display_name||"Unassigned",booking_date:e.booking_date,booking_time:s,start_time:e.start_time,end_time:e.end_time,status:e.status,total_amount:e.total_amount,notes:e.notes,created_at:e.created_at}});return t.status(200).json({bookings:d,total:d.length})}catch(e){return console.error("Customer bookings API error:",e),t.status(500).json({error:"Internal server error"})}}let m=(0,o.l)(s,"default"),_=(0,o.l)(s,"config"),p=new a.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/customers/[id]/bookings",pathname:"/api/admin/customers/[id]/bookings",bundlePath:"",filename:""},userland:s})}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[805],()=>r(544));module.exports=s})();