"use strict";(()=>{var e={};e.id=20,e.ids=[20],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},7191:e=>{e.exports=require("square")},8576:(e,r,s)=>{s.r(r),s.d(r,{config:()=>u,default:()=>d,routeModule:()=>l});var t={};s.r(t),s.d(t,{default:()=>c});var a=s(1802),o=s(7153),n=s(8781);s(8456);var i=s(7474);async function c(e,r){if("POST"!==e.method)return r.status(405).json({error:"Method not allowed"});let t=Math.random().toString(36).substring(2,8);console.log(`[${t}] Square payment processing API called`);try{let{user:a,error:o}=await (0,i.authenticateAdminRequest)(e);if(o||!a)return r.status(401).json({error:"Authentication required",message:o?.message||"Authentication failed",requestId:t});let{sourceId:n,amountMoney:c,orderDetails:d,billingContact:u}=e.body;if(!n||!c)return r.status(400).json({error:"Missing required fields",message:"sourceId and amountMoney are required",requestId:t});let l=c.amount,p=c.currency||"AUD";if(!l||l<=0)return r.status(400).json({error:"Invalid payment amount",message:"Payment amount must be greater than 0",requestId:t});console.log(`[${t}] Processing Square payment for amount: ${l} ${p}`);let m=process.env.SQUARE_ACCESS_TOKEN,y="sq0idp-placeholder",g="location-placeholder",S=process.env.SQUARE_ENVIRONMENT||"sandbox";if(!m||!y||!g)return console.error(`[${t}] Square configuration missing:`,{hasAccessToken:!!m,hasApplicationId:!!y,hasLocationId:!!g}),r.status(500).json({error:"Square configuration missing",message:"Square payment processing is not properly configured",requestId:t});let{Client:v}=s(7191),I=new v({accessToken:m,environment:"production"===S?"production":"sandbox"}).paymentsApi,q={sourceId:n,idempotencyKey:`pos_${t}_${Date.now()}`,amountMoney:{amount:l,currency:p},locationId:g,note:d?.service||"POS Payment",buyerEmailAddress:d?.customerEmail||void 0};u&&(q.billingAddress={addressLine1:u.addressLines?.[0],addressLine2:u.addressLines?.[1],locality:u.locality,administrativeDistrictLevel1:u.administrativeDistrictLevel1,postalCode:u.postalCode,country:u.country}),console.log(`[${t}] Making Square API request:`,{amount:l,currency:p,locationId:g,environment:S});let{result:f,statusCode:A}=await I.createPayment(q);if(console.log(`[${t}] Square API response status:`,A),200===A&&f.payment){let e=f.payment;return console.log(`[${t}] Square payment successful:`,e.id),r.status(200).json({success:!0,paymentId:e.id,paymentStatus:e.status,paymentDetails:{id:e.id,status:e.status,totalMoney:e.totalMoney,approvedMoney:e.approvedMoney,processingFee:e.processingFee,cardDetails:e.cardDetails?{status:e.cardDetails.status,card:{cardBrand:e.cardDetails.card?.cardBrand,last4:e.cardDetails.card?.last4,cardType:e.cardDetails.card?.cardType},avsStatus:e.cardDetails.avsStatus,cvvStatus:e.cardDetails.cvvStatus}:void 0,receiptNumber:e.receiptNumber,receiptUrl:e.receiptUrl,createdAt:e.createdAt,updatedAt:e.updatedAt},requestId:t})}{console.error(`[${t}] Square payment failed:`,f);let e=f.errors?.[0]?.detail||"Payment processing failed",s=f.errors?.[0]?.code||"UNKNOWN_ERROR";return r.status(400).json({error:"Payment failed",message:e,errorCode:s,requestId:t})}}catch(e){if(console.error(`[${t}] Square payment processing error:`,e),e.errors&&Array.isArray(e.errors)){let s=e.errors[0];return r.status(400).json({error:"Square payment error",message:s.detail||"Payment processing failed",errorCode:s.code||"SQUARE_ERROR",requestId:t})}return r.status(500).json({error:"Internal server error",message:"An unexpected error occurred during payment processing",requestId:t})}}let d=(0,n.l)(t,"default"),u=(0,n.l)(t,"config"),l=new a.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/pos/process-payment",pathname:"/api/admin/pos/process-payment",bundlePath:"",filename:""},userland:t})},8456:(e,r,s)=>{s.d(r,{pR:()=>i});var t=s(2885);let a="https://ndlgbcsbidyhxbpqzgqp.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!a||!o)throw Error("Missing Supabase environment variables");(0,t.createClient)(a,o,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let i=(0,t.createClient)(a,n||o,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[805],()=>s(8576));module.exports=t})();