import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    // Get bookings with customer and service details
    let query = supabase
      .from('bookings')
      .select(`
        id,
        booking_date,
        booking_time,
        status,
        total_amount,
        notes,
        created_at,
        customer_id,
        service_id,
        artist_id,
        customers (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        services (
          id,
          name,
          duration,
          price
        ),
        user_profiles!artist_id (
          id,
          name
        )
      `)
      .order('booking_date', { ascending: false })
      .order('booking_time', { ascending: false });

    // If user is an artist, only show their bookings
    if (user.role === 'Artist' || user.role === 'Braider') {
      query = query.eq('artist_id', user.id);
    }

    const { data: bookings, error } = await query;

    if (error) {
      console.error('Bookings query error:', error);
      return res.status(500).json({ error: 'Failed to fetch bookings' });
    }    // Transform data to match expected format
    const transformedBookings = (bookings || []).map(booking => {
      const customer = Array.isArray(booking.customers) ? booking.customers[0] : booking.customers;
      const service = Array.isArray(booking.services) ? booking.services[0] : booking.services;
      const artist = Array.isArray(booking.user_profiles) ? booking.user_profiles[0] : booking.user_profiles;
      
      return {
        id: booking.id,
        customer_name: customer ? 
          `${customer.first_name} ${customer.last_name}` : 'Unknown Customer',
        customer_email: customer?.email || '',
        customer_phone: customer?.phone || '',
        service_name: service?.name || 'Unknown Service',
        service_duration: service?.duration || 0,
        service_price: service?.price || 0,
        artist_name: artist?.name || 'Unassigned',
        booking_date: booking.booking_date,
        booking_time: booking.booking_time,
        start_time: `${booking.booking_date}T${booking.booking_time}`,
        status: booking.status,
        total_amount: booking.total_amount,
        notes: booking.notes,
        created_at: booking.created_at
      };
    });

    return res.status(200).json({
      bookings: transformedBookings,
      total: transformedBookings.length
    });

  } catch (error) {
    console.error('Bookings API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
